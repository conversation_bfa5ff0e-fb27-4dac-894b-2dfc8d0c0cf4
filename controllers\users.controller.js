const User = require("../models/users.model");
const Report = require("../models/report.model");
const Feed = require("../models/feed.model");
const { deleteFile } = require("../middlewares/upload.middleware");
const path = require("path");

// Calculate user badge based on report count
const calculateBadge = (reportCount) => {
  if (reportCount >= 100) return "Diamond";
  if (reportCount >= 50) return "Gold";
  if (reportCount >= 25) return "Silver";
  if (reportCount >= 10) return "Bronze";
  return null;
};

// Get user profile
const getUserProfile = async (req, res) => {
  try {
    const userId = req.userId;

    // Get user details
    const user = await User.findById(userId).select("-password");
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Get report count
    const reportCount = await Report.countDocuments({ userId });

    // Calculate badge
    const badge = calculateBadge(reportCount);

    // Get total likes done by user
    const totalLikesDone = await Feed.countDocuments({
      likes: userId,
    });

    // Prepare response
    const profileData = {
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      countryCode: user.countryCode,
      mobile: user.mobile,
      gender: user.gender,
      profilePic: user.profilePic
        ? `${process.env.BASE_URL}/images/${user.profilePic}`
        : null,
      reportCount,
      badge,
      totalLikesDone,
      joinedAt: user.createdAt,
    };

    res.status(200).json({
      success: true,
      data: {
        profile: profileData,
      },
    });
  } catch (error) {
    console.error("Get profile error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch profile",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Update user profile
const updateUserProfile = async (req, res) => {
  try {
    const userId = req.userId;
    const { firstName, lastName, countryCode, mobile, gender, city, state } =
      req.body;

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Check if mobile number is being updated and if it conflicts with existing users
    if (mobile || countryCode) {
      const newCountryCode = countryCode || user.countryCode;
      const newMobile = mobile || user.mobile;

      // Check if this combination already exists for another user
      const existingUser = await User.findOne({
        _id: { $ne: userId }, // Exclude current user
        countryCode: newCountryCode,
        mobile: newMobile,
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: "Mobile number already exists for this country",
        });
      }
    }

    // Prepare update data
    const updateData = {};
    if (firstName) updateData.firstName = firstName;
    if (lastName) updateData.lastName = lastName;
    if (countryCode) updateData.countryCode = countryCode;
    if (mobile) updateData.mobile = mobile;
    if (gender) updateData.gender = gender;
    if (city) updateData.city = city.trim();
    if (state) updateData.state = state.trim();

    // Handle profile picture upload
    if (req.file) {
      // Delete old profile picture if exists
      if (user.profilePic) {
        const oldImagePath = path.join(
          __dirname,
          "../uploads",
          user.profilePic
        );
        deleteFile(oldImagePath);
      }
      updateData.profilePic = req.file.filename;
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(userId, updateData, {
      new: true,
      runValidators: true,
    }).select("-password");

    res.status(200).json({
      success: true,
      message: "Profile updated successfully",
      data: {
        user: {
          ...updatedUser.toObject(),
          profilePic: updatedUser.profilePic
            ? `${process.env.BASE_URL}/images/${updatedUser.profilePic}`
            : null,
        },
      },
    });
  } catch (error) {
    // Clean up uploaded file if update failed
    if (req.file) {
      deleteFile(req.file.path);
    }

    console.error("Update profile error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update profile",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Get user's reports
const getUserReports = async (req, res) => {
  try {
    const userId = req.userId;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get user's reports with pagination
    const reports = await Report.find({ userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const totalReports = await Report.countDocuments({ userId });
    const totalPages = Math.ceil(totalReports / limit);

    // Add image URLs to reports
    const reportsWithImages = reports.map((report) => ({
      ...report.toObject(),
      imageUrl: report.imagePath
        ? `${process.env.BASE_URL}/images/${report.imagePath}`
        : null,
    }));

    res.status(200).json({
      success: true,
      data: {
        reports: reportsWithImages,
        pagination: {
          currentPage: page,
          totalPages,
          totalReports,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      },
    });
  } catch (error) {
    console.error("Get user reports error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch user reports",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Get user's upvoted feeds
const getUserLikes = async (req, res) => {
  try {
    const userId = req.userId;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get feeds that user has liked
    const feeds = await Feed.find({
      likes: userId,
      isActive: true,
    })
      .populate("userId", "firstName lastName profilePic")
      .sort({ postedAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const totalUpvotes = await Feed.countDocuments({
      likes: userId,
      isActive: true,
    });
    const totalPages = Math.ceil(totalUpvotes / limit);

    // Add image URLs and format response
    const feedsWithDetails = feeds.map((feed) => ({
      ...feed.toObject(),
      imageUrl: feed.image
        ? `${process.env.BASE_URL}/images/${feed.image}`
        : null,
      userProfilePic: feed.userId.profilePic
        ? `${process.env.BASE_URL}/images/${feed.userId.profilePic}`
        : null,
      isLikedByCurrentUser: true, // Since these are user's upvoted feeds
    }));

    res.status(200).json({
      success: true,
      data: {
        feeds: feedsWithDetails,
        pagination: {
          currentPage: page,
          totalPages,
          totalUpvotes,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      },
    });
  } catch (error) {
    console.error("Get user upvotes error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch user upvotes",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

module.exports = {
  getUserProfile,
  updateUserProfile,
  getUserReports,
  getUserLikes,
};
