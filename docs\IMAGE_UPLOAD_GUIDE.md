# Image Upload Guide for Mobile Team

## Overview
The CivicSafe backend supports image uploads using **multipart/form-data** format. Images are stored locally on the server and accessible via direct URLs.

## Supported Image Formats
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)

## File Size Limits
- Maximum file size: **5MB**
- Recommended size: **1-2MB** for optimal performance

## Upload Endpoints

### 1. Profile Picture Upload
```http
PUT /api/v1/user/profile
Authorization: Bearer <token>
Content-Type: multipart/form-data

Form Data:
- profilePic: <image_file>
- firstName: "John"
- lastName: "Doe"
- mobile: "9876543210"
- gender: "male"
```

### 2. Report Image Upload
```http
POST /api/v1/reports
Authorization: Bearer <token>
Content-Type: multipart/form-data

Form Data:
- image: <image_file>
- location: "Downtown"
- description: "Pothole issue"
- issueType: "infrastructure"
- affectedRole: "citizen"
- dateOfIncident: "2024-01-15"
- timeOfIncident: "14:30"
```

### 3. Feed Image Upload
```http
POST /api/v1/feeds
Authorization: Bearer <token>
Content-Type: multipart/form-data

Form Data:
- image: <image_file>
- title: "Community Meeting"
- description: "Join us for monthly meeting"
- category: "announcement"
```

## Mobile Implementation Examples

### React Native Example
```javascript
const uploadProfilePicture = async (imageUri, userData) => {
  const formData = new FormData();
  
  // Add image file
  formData.append('profilePic', {
    uri: imageUri,
    type: 'image/jpeg',
    name: 'profile.jpg',
  });
  
  // Add other form fields
  formData.append('firstName', userData.firstName);
  formData.append('lastName', userData.lastName);
  formData.append('mobile', userData.mobile);
  formData.append('gender', userData.gender);
  
  try {
    const response = await fetch('http://localhost:3000/api/v1/user/profile', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
        'x-user-timezone': 'Asia/Kolkata', // User's timezone
      },
      body: formData,
    });
    
    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Upload failed:', error);
  }
};
```

### Flutter Example
```dart
Future<Map<String, dynamic>> uploadReportImage(
  File imageFile, 
  Map<String, String> reportData
) async {
  var request = http.MultipartRequest(
    'POST', 
    Uri.parse('http://localhost:3000/api/v1/reports')
  );
  
  // Add headers
  request.headers['Authorization'] = 'Bearer $token';
  request.headers['x-user-timezone'] = 'Asia/Kolkata';
  
  // Add image file
  request.files.add(
    await http.MultipartFile.fromPath('image', imageFile.path)
  );
  
  // Add form fields
  request.fields.addAll(reportData);
  
  var response = await request.send();
  var responseData = await response.stream.bytesToString();
  
  return json.decode(responseData);
}
```

## Image URL Response Format

When images are uploaded successfully, the server returns URLs in this format:

```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "_id": "user_id",
      "firstName": "John",
      "lastName": "Doe",
      "profilePic": "http://localhost:3000/images/profiles/1234567890_profile.jpg",
      "createdAt": {
        "utc": "2025-08-02 12:00:18",
        "userTimezone": "2025-08-02 17:30:18",
        "timezone": "Asia/Kolkata"
      }
    }
  }
}
```

## Image Access URLs

### Direct Image Access
```
GET http://localhost:3000/images/profiles/1234567890_profile.jpg
GET http://localhost:3000/images/reports/1234567890_report.jpg
GET http://localhost:3000/images/feeds/1234567890_feed.jpg
```

### Image Categories
- **Profile Pictures**: `/images/profiles/`
- **Report Images**: `/images/reports/`
- **Feed Images**: `/images/feeds/`

## Error Handling

### Common Error Responses
```json
{
  "success": false,
  "message": "File too large. Maximum size allowed is 5MB"
}

{
  "success": false,
  "message": "Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed"
}

{
  "success": false,
  "message": "No file uploaded"
}
```

## Best Practices for Mobile Team

### 1. Image Compression
- Compress images before upload to reduce file size
- Recommended dimensions: 800x600 for reports, 400x400 for profiles

### 2. Error Handling
- Always check response status and handle errors gracefully
- Implement retry mechanism for failed uploads

### 3. Progress Tracking
- Show upload progress to users
- Implement cancel functionality

### 4. Caching
- Cache image URLs locally to avoid repeated downloads
- Implement image caching strategy

### 5. Timezone Headers
- Always send user's timezone in `x-user-timezone` header
- Format: 'Asia/Kolkata', 'America/New_York', etc.

## Security Notes

1. **File Validation**: Server validates file type and size
2. **Automatic Cleanup**: Failed uploads are automatically cleaned up
3. **Secure Storage**: Images are stored outside web root with controlled access
4. **Authentication**: All upload endpoints require valid JWT token

## Testing with cURL

```bash
# Test profile picture upload
curl -X PUT http://localhost:3000/api/v1/user/profile \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "x-user-timezone: Asia/Kolkata" \
  -F "profilePic=@/path/to/image.jpg" \
  -F "firstName=John" \
  -F "lastName=Doe"

# Test report image upload
curl -X POST http://localhost:3000/api/v1/reports \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "x-user-timezone: Asia/Kolkata" \
  -F "image=@/path/to/image.jpg" \
  -F "location=Downtown" \
  -F "description=Pothole issue"
```

## Summary for Mobile Team

**Use multipart/form-data format for all image uploads. The server will:**
1. Validate file type and size
2. Store images locally with unique names
3. Return direct URLs for image access
4. Handle timezone conversion based on headers
5. Provide proper error messages for failed uploads

**Always include the `x-user-timezone` header for proper date/time handling.**
