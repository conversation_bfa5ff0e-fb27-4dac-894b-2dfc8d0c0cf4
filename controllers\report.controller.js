const Report = require("../models/report.model");
const { notifyUsersByCity } = require("../utils/notification.util");
const { deleteFile } = require("../middlewares/upload.middleware");
const { formatDateForResponse } = require("../utils/timezone.util");
const { getBaseUrl } = require("../utils/common.util");

// Create a new report
const createReport = async (req, res) => {
  try {
    const { location, description, issueType, affectedRole, incidentDateTime } =
      req.body;
    const userId = req.userId;

    // Prepare report data
    const reportData = {
      userId,
      location,
      description,
      issueType,
      affectedRole,
      incidentDateTime: new Date(incidentDateTime),
    };

    // Add image path if file was uploaded
    if (req.file) {
      reportData.imagePath = req.file.filename;
    }

    // Create report
    const report = await Report.create(reportData);

    // Populate user details for response
    await report.populate("userId", "firstName lastName email");

    // Trigger notification to users in the same city (async, don't wait)
    notifyUsersByCity(location, {
      issueType,
      reportId: report._id.toString(),
      description:
        description.substring(0, 100) + (description.length > 100 ? "..." : ""),
    }).catch((err) => console.error("Error sending notifications:", err));

    // Format response with timezone conversion
    const reportResponse = report.toObject();

    // Convert UTC dates to user timezone
    // if (reportResponse.incidentDateTime) {
    //   reportResponse.incidentDateTime = formatDateForResponse(
    //     reportResponse.incidentDateTime,
    //     req.userTimezone
    //   );
    // }
    // if (reportResponse.createdAt) {
    //   reportResponse.createdAt = formatDateForResponse(
    //     reportResponse.createdAt,
    //     req.userTimezone
    //   );
    // }
    // if (reportResponse.updatedAt) {
    //   reportResponse.updatedAt = formatDateForResponse(
    //     reportResponse.updatedAt,
    //     req.userTimezone
    //   );
    // }

    res.status(201).json({
      success: true,
      message: "Report created successfully",
      // data: {
      //   report: {
      //     ...reportResponse,
      //     imageUrl: report.imagePath ? `${process.env.BASE_URL}/images/${report.imagePath}` : null
      //   }
      // }
    });
  } catch (error) {
    // Clean up uploaded file if report creation failed
    if (req.file) {
      deleteFile(req.file.path);
    }

    console.error("Create report error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create report",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Get all reports with pagination
const getAllReports = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    if (req.query.location) {
      filter.location = { $regex: req.query.location, $options: "i" };
    }
    if (req.query.issueType) {
      filter.issueType = req.query.issueType;
    }
    if (req.query.status) {
      filter.status = req.query.status;
    }

    // Get reports with pagination
    const reports = await Report.find(filter)
      .populate("userId", "firstName lastName")
      .sort({ createdAt: -1 })
      .select("-__v -updatedAt")
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const totalReports = await Report.countDocuments(filter);
    const totalPages = Math.ceil(totalReports / limit);

    // Add image URLs and format dates for reports
    const reportsWithImages = reports.map((report) => {
      const reportObj = report.toObject();

      // Convert UTC dates to user timezone
      // if (reportObj.incidentDateTime) {
      //   reportObj.incidentDateTime = formatDateForResponse(
      //     reportObj.incidentDateTime,
      //     req.userTimezone
      //   );
      // }
      // if (reportObj.createdAt) {
      //   reportObj.createdAt = formatDateForResponse(
      //     reportObj.createdAt,
      //     req.userTimezone
      //   );
      // }
      // if (reportObj.updatedAt) {
      //   reportObj.updatedAt = formatDateForResponse(
      //     reportObj.updatedAt,
      //     req.userTimezone
      //   );
      // }

      return {
        ...reportObj,
        imageUrl: report.imagePath
          ? `${getBaseUrl(req)}/images/${report.imagePath}`
          : null,
      };
    });

    res.status(200).json({
      success: true,
      data: {
        reports: reportsWithImages,
        pagination: {
          currentPage: page,
          totalPages,
          totalReports,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      },
    });
  } catch (error) {
    console.error("Get reports error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch reports",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Get single report by ID
const getReportById = async (req, res) => {
  try {
    const { id } = req.params;

    const report = await Report.findById(id).populate(
      "userId",
      "firstName lastName email mobile"
    );

    if (!report) {
      return res.status(404).json({
        success: false,
        message: "Report not found",
      });
    }

    // Format response with timezone conversion
    const reportResponse = report.toObject();

    // Convert UTC dates to user timezone
    if (reportResponse.incidentDateTime) {
      reportResponse.incidentDateTime = formatDateForResponse(
        reportResponse.incidentDateTime,
        req.userTimezone
      );
    }
    if (reportResponse.createdAt) {
      reportResponse.createdAt = formatDateForResponse(
        reportResponse.createdAt,
        req.userTimezone
      );
    }
    if (reportResponse.updatedAt) {
      reportResponse.updatedAt = formatDateForResponse(
        reportResponse.updatedAt,
        req.userTimezone
      );
    }

    res.status(200).json({
      success: true,
      data: {
        report: {
          ...reportResponse,
          imageUrl: report.imagePath
            ? `${getBaseUrl(req)}/images/${report.imagePath}`
            : null,
        },
      },
    });
  } catch (error) {
    console.error("Get report error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch report",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Update report status (admin functionality)
const updateReportStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const report = await Report.findByIdAndUpdate(
      id,
      { status },
      { new: true, runValidators: true }
    ).populate("userId", "firstName lastName");

    if (!report) {
      return res.status(404).json({
        success: false,
        message: "Report not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Report status updated successfully",
      data: {
        report: {
          ...report.toObject(),
          imageUrl: report.imagePath
            ? `${getBaseUrl(req)}/images/${report.imagePath}`
            : null,
        },
      },
    });
  } catch (error) {
    console.error("Update report status error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update report status",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Delete report (user can only delete their own reports)
const deleteReport = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.userId;

    const report = await Report.findOne({ _id: id, userId });

    if (!report) {
      return res.status(404).json({
        success: false,
        message:
          "Report not found or you are not authorized to delete this report",
      });
    }

    // Delete associated image file
    if (report.imagePath) {
      const imagePath = path.join(__dirname, "../uploads", report.imagePath);
      deleteFile(imagePath);
    }

    await Report.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: "Report deleted successfully",
    });
  } catch (error) {
    console.error("Delete report error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete report",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

module.exports = {
  createReport,
  getAllReports,
  getReportById,
  updateReportStatus,
  deleteReport,
};
