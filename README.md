# CivicSafe Server

A production-ready Node.js + Express backend server for the CivicSafe mobile application. This server provides APIs for civic issue reporting, community feeds, user management, and push notifications.

## 🚀 Features

- **Authentication System**: JWT-based authentication with registration, login, and password reset
- **Report Management**: Create, view, and manage civic issue reports with image uploads
- **Community Feeds**: Social feed system with posts, likes, and comments
- **User Profiles**: Profile management with badges based on activity
- **Push Notifications**: Firebase-based notifications for community updates
- **Image Upload**: Secure image handling with Multer
- **Security**: Helmet, CORS, rate limiting, and input validation
- **Logging**: Morgan-based HTTP request logging
- **Error Handling**: Comprehensive error handling and cleanup

## 🛠️ Technology Stack

- **Runtime**: Node.js (Latest LTS)
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (jsonwebtoken)
- **Password Hashing**: Node.js crypto (PBKDF2)
- **File Upload**: Multer
- **Email**: <PERSON>demailer
- **Push Notifications**: Firebase Admin SDK
- **Security**: Helmet, CORS, express-rate-limit
- **Validation**: express-validator
- **Logging**: Morgan

## 📁 Project Structure

```
civicsafe-server/
├── controllers/          # Route controllers
│   ├── auth.controller.js
│   ├── feed.controller.js
│   ├── report.controller.js
│   ├── settings.controller.js
│   └── users.controller.js
├── middlewares/          # Custom middlewares
│   ├── auth.middleware.js
│   ├── error.middleware.js
│   ├── security.middleware.js
│   └── upload.middleware.js
├── models/              # Database models
│   ├── feed.model.js
│   ├── report.model.js
│   └── users.model.js
├── routes/              # API routes
│   ├── auth.route.js
│   ├── feed.route.js
│   ├── index.js
│   ├── report.route.js
│   ├── settings.route.js
│   └── users.route.js
├── utils/               # Utility functions
│   ├── email.util.js
│   ├── notification.util.js
│   └── password.util.js
├── config/              # Configuration files
│   └── firebaseServiceAccount.example.json
├── uploads/             # Uploaded images
├── logs/               # Application logs
├── index.js            # Main server file
├── .env.example        # Environment variables template
└── package.json        # Dependencies and scripts
```

## 🚦 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- MongoDB (local or cloud)
- Gmail account (for email features)
- Firebase project (for push notifications)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/civicsafe/civicsafe-server.git
   cd civicsafe-server
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```

   Edit `.env` file with your configuration:
   ```env
   PORT=5000
   MONGO_URI=mongodb://localhost:27017/civicsafe
   JWT_SECRET=your_super_secret_jwt_key_here
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   BASE_URL=http://localhost:5000
   ```

4. **Firebase Setup (Optional)**
   - Create a Firebase project
   - Generate a service account key
   - Save it as `config/firebaseServiceAccount.json`

5. **Start the server**
   ```bash
   # Development
   npm run dev

   # Production
   npm start
   ```

The server will start on `http://localhost:5000`

## 📚 API Documentation

### Base URL
```
http://localhost:5000/api/v1
```

### Authentication Endpoints

#### Register User
```http
POST /auth/register
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "mobile": "**********",
  "gender": "male",
  "password": "Password123",
  "fcmToken": "optional_fcm_token"
}
```

#### Login User
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password123",
  "fcmToken": "optional_fcm_token"
}
```

#### Forgot Password
```http
POST /auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

### Report Endpoints

#### Create Report
```http
POST /reports
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "location": "Downtown",
  "description": "Pothole on main street",
  "issueType": "infrastructure",
  "affectedRole": "citizen",
  "dateOfIncident": "2024-01-15",
  "timeOfIncident": "14:30",
  "image": <file>
}
```

#### Get All Reports
```http
GET /reports?page=1&limit=10&location=downtown&issueType=infrastructure
```

### Feed Endpoints

#### Create Feed Post
```http
POST /feeds
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "title": "Community Meeting",
  "description": "Join us for the monthly community meeting",
  "category": "announcement",
  "image": <file>
}
```

#### Get All Feeds
```http
GET /feeds?page=1&limit=10&category=announcement
```

#### Like/Unlike Feed
```http
POST /feeds/:id/like
Authorization: Bearer <token>
```

#### Add Comment
```http
POST /feeds/:id/comment
Authorization: Bearer <token>
Content-Type: application/json

{
  "comment": "Great initiative!"
}
```

### User Profile Endpoints

#### Get Profile
```http
GET /user/profile
Authorization: Bearer <token>
```

#### Update Profile
```http
PUT /user/profile
Authorization: Bearer <token>
Content-Type: multipart/form-data
x-user-timezone: Asia/Kolkata

{
  "firstName": "John",
  "lastName": "Doe",
  "mobile": "**********",
  "gender": "male",
  "profilePic": <file>
}
```

#### Get User Reports
```http
GET /user/reports?page=1&limit=10
Authorization: Bearer <token>
```

#### Get User Upvotes
```http
GET /user/upvotes?page=1&limit=10
Authorization: Bearer <token>
```

### Settings Endpoints

#### Privacy Policy
```http
GET /settings/privacy-policy
```

#### About Us
```http
GET /settings/about-us
```

## 🔒 Security Features

- **Helmet**: Security headers
- **CORS**: Cross-origin resource sharing
- **Rate Limiting**: API rate limiting
- **Input Validation**: Request validation with express-validator
- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: PBKDF2 with crypto module
- **File Upload Security**: Secure image upload with type validation

## 🏆 User Badge System

Users earn badges based on their report count:
- **Bronze**: 10-24 reports
- **Silver**: 25-49 reports
- **Gold**: 50-99 reports
- **Diamond**: 100+ reports

## 📱 Push Notifications

The server supports Firebase Cloud Messaging (FCM) for push notifications:
- New reports in user's city
- Feed interactions
- System announcements

## 🗂️ File Management

- Images are stored in the `uploads/` directory
- Accessible via `/images/<filename>` endpoint
- Automatic cleanup on failed operations
- File type validation (JPEG, PNG, GIF, WebP)
- 5MB file size limit

## 🔧 Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `PORT` | Server port | No (default: 5000) |
| `MONGO_URI` | MongoDB connection string | Yes |
| `JWT_SECRET` | JWT signing secret | Yes |
| `JWT_EXPIRES_IN` | JWT expiration time | No (default: 30d) |
| `EMAIL_USER` | Email username | Yes |
| `EMAIL_PASS` | Email password | Yes |
| `BASE_URL` | Base URL for file serving | Yes |

## 🌍 Timezone Handling

The server handles all dates and times in UTC format internally. For proper timezone support:

### Request Headers
Include user's timezone in all requests:
```http
x-user-timezone: Asia/Kolkata
```

### Response Format
All datetime fields return both UTC and user timezone:
```json
{
  "createdAt": {
    "utc": "2025-08-02 12:00:18",
    "userTimezone": "2025-08-02 17:30:18",
    "timezone": "Asia/Kolkata"
  }
}
```

### Supported Timezones
- Asia/Kolkata
- America/New_York
- Europe/London
- Australia/Sydney
- And all other IANA timezone identifiers

## 📸 Image Upload System

### Upload Format
Use **multipart/form-data** for all image uploads:

```javascript
// React Native example
const formData = new FormData();
formData.append('profilePic', {
  uri: imageUri,
  type: 'image/jpeg',
  name: 'profile.jpg',
});
```

### Image Storage
- **Local Storage**: Images stored in `uploads/` directory
- **Direct URLs**: Accessible via `/images/<category>/<filename>`
- **Categories**: profiles, reports, feeds
- **Formats**: JPEG, PNG, GIF, WebP
- **Size Limit**: 5MB maximum

### Image URLs in Response
```json
{
  "profilePic": "http://localhost:3000/images/profiles/1234567890_profile.jpg"
}
```

## 🚀 Deployment

### Using PM2 (Recommended)

1. Install PM2 globally:
   ```bash
   npm install -g pm2
   ```

2. Start the application:
   ```bash
   pm2 start index.js --name "civicsafe-server"
   ```

3. Save PM2 configuration:
   ```bash
   pm2 save
   pm2 startup
   ```

### Using Docker

1. Create a Dockerfile:
   ```dockerfile
   FROM node:16-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci --only=production
   COPY . .
   EXPOSE 5000
   CMD ["npm", "start"]
   ```

2. Build and run:
   ```bash
   docker build -t civicsafe-server .
   docker run -p 5000:5000 civicsafe-server
   ```

## 🧪 Testing

Run the server and test endpoints using tools like:
- Postman
- Insomnia
- curl
- Thunder Client (VS Code extension)

Example health check:
```bash
curl http://localhost:5000/health
```

## 📝 Logging

- HTTP requests are logged using Morgan
- Logs are stored in `logs/access.log`
- Console logging for development
- Error logging with stack traces in development mode

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.

## 👥 Authors

- **Kirtan Gajjar** - Initial work

## 🆘 Support

For support, email <EMAIL> or create an issue in the repository.

---

**Made with ❤️ for building better communities**