const crypto = require("crypto");
const jwt = require("jsonwebtoken");
const User = require("../models/users.model");
const {
  sendPasswordResetEmail,
  sendWelcomeEmail,
} = require("../utils/email.util");
const { generateRandomPassword } = require("../utils/password.util");
const { updateUserFCMToken } = require("../utils/notification.util");
const { formatDateForResponse } = require("../utils/timezone.util");

// Crypto helper functions to replace bcryptjs
const hashPassword = async (password, saltRounds = 12) => {
  return new Promise((resolve, reject) => {
    // Generate a random salt
    const salt = crypto.randomBytes(16).toString("hex");

    // Use pbkdf2 for password hashing (similar to bcrypt's approach)
    crypto.pbkdf2(password, salt, 100000, 64, "sha512", (err, derivedKey) => {
      if (err) reject(err);
      else resolve(salt + ":" + derivedKey.toString("hex"));
    });
  });
};

const comparePassword = async (password, hash) => {
  return new Promise((resolve, reject) => {
    const [salt, key] = hash.split(":");

    crypto.pbkdf2(password, salt, 100000, 64, "sha512", (err, derivedKey) => {
      if (err) reject(err);
      else resolve(key === derivedKey.toString("hex"));
    });
  });
};

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || "30d",
  });
};

// Register new user
const register = async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      email,
      countryCode,
      mobile,
      gender,
      password,
      fcmToken,
      city,
      state,
    } = req.body;

    // Normalize email (keep original format, just trim and lowercase for comparison)
    const normalizedEmail = email.trim().toLowerCase();

    // Check if user already exists (by email or by countryCode + mobile combination)
    const existingUser = await User.findOne({
      $or: [{ email: normalizedEmail }, { countryCode, mobile }],
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message:
          existingUser.email === normalizedEmail
            ? "Email already exists"
            : "Mobile number already exists for this country",
      });
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user
    const user = await User.create({
      firstName,
      lastName,
      email: email.trim(), // Store original email format but trimmed
      countryCode,
      mobile,
      gender,
      password: hashedPassword,
      fcmToken,
      city: city ? city.trim() : undefined,
      state: state ? state.trim() : undefined,
      profilePic: req.file ? req.file.filename : null,
    });

    // Generate token
    const token = generateToken(user._id);

    // Send welcome email (don't wait for it)
    sendWelcomeEmail(email, firstName).catch((err) =>
      console.error("Error sending welcome email:", err)
    );

    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.password;

    // Format dates with timezone
    if (userResponse.createdAt) {
      userResponse.createdAt = formatDateForResponse(
        userResponse.createdAt,
        req.userTimezone
      );
    }
    if (userResponse.updatedAt) {
      userResponse.updatedAt = formatDateForResponse(
        userResponse.updatedAt,
        req.userTimezone
      );
    }

    res.status(201).json({
      success: true,
      message: "User registered successfully",
      data: {
        user: {
          ...userResponse,
          profilePic: userResponse.profilePic
            ? `${process.env.BASE_URL}/images/${userResponse.profilePic}`
            : null,
        },
        token,
      },
    });
  } catch (error) {
    console.error("Registration error:", error);
    res.status(500).json({
      success: false,
      message: "Registration failed",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Login user
const login = async (req, res) => {
  try {
    const { email, password, fcmToken } = req.body;

    // Normalize email for lookup
    const normalizedEmail = email.trim().toLowerCase();

    // Find user and include password for verification
    const user = await User.findOne({
      // email: { $regex: new RegExp(`^${normalizedEmail}$`, "i") },
      email: normalizedEmail,
    }).select("+password");

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Invalid email or password",
      });
    }

    // Verify password
    const isPasswordValid = await comparePassword(password, user.password);

    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Invalid email or password",
      });
    }

    // Update FCM token if provided
    if (fcmToken) {
      user.fcmToken = fcmToken;
      await user.save();
    }

    // Generate token
    const token = generateToken(user._id);

    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.password;
    delete userResponse.updatedAt;
    delete userResponse.__v;

    // Format dates with timezone
    // if (userResponse.createdAt) {
    //   userResponse.createdAt = formatDateForResponse(
    //     userResponse.createdAt,
    //     req.userTimezone
    //   );
    // }
    // if (userResponse.updatedAt) {
    //   userResponse.updatedAt = formatDateForResponse(
    //     userResponse.updatedAt,
    //     req.userTimezone
    //   );
    // }

    res.status(200).json({
      success: true,
      message: "Login successful",
      // data: {
      user: userResponse,
      token,
      // },
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({
      success: false,
      message: "Login failed",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Forgot password
const forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    // Normalize email for lookup
    const normalizedEmail = email.trim().toLowerCase();

    // Check if user exists
    const user = await User.findOne({
      email: { $regex: new RegExp(`^${normalizedEmail}$`, "i") },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found with this email",
      });
    }

    // Generate new random password
    const newPassword = generateRandomPassword(10);

    // Hash the new password
    const hashedPassword = await hashPassword(newPassword);

    // Update user's password in database
    await User.findByIdAndUpdate(user._id, { password: hashedPassword });

    // Send email with new password
    const emailResult = await sendPasswordResetEmail(email, newPassword);

    if (!emailResult.success) {
      return res.status(500).json({
        success: false,
        message: "Failed to send password reset email",
      });
    }

    res.status(200).json({
      success: true,
      message: "Password reset email sent successfully",
    });
  } catch (error) {
    console.error("Forgot password error:", error);
    res.status(500).json({
      success: false,
      message: "Password reset failed",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

module.exports = {
  register,
  login,
  forgotPassword,
};
