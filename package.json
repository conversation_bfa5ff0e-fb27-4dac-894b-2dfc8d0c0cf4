{"name": "civicsafe-server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "repository": {"type": "git", "url": "git+https://github.com/civicsafe/civicsafe-server.git"}, "author": "Kirtan <PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/civicsafe/civicsafe-server/issues"}, "homepage": "https://github.com/civicsafe/civicsafe-server#readme", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "firebase-admin": "^13.4.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "luxon": "^3.7.1", "mongoose": "^8.17.0", "morgan": "^1.10.1", "multer": "^2.0.2", "nodemailer": "^7.0.5"}, "devDependencies": {"nodemon": "^3.1.10"}}