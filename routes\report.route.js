const router = require("express").Router();
const reportController = require("../controllers/report.controller");
const {
  authenticateToken,
  optionalAuth,
} = require("../middlewares/auth.middleware");
const {
  uploadSingle,
  cleanupOnError,
} = require("../middlewares/upload.middleware");
const {
  validateReport,
  generalLimiter,
} = require("../middlewares/security.middleware");

// Create report (authenticated)
router.post(
  "/",
  authenticateToken,
  generalLimiter,
  uploadSingle("image"),
  cleanupOnError,
  validateReport,
  reportController.createReport
);

// Get all reports (public with optional auth for user context)
router.get("/", optionalAuth, reportController.getAllReports);

// Get single report (public with optional auth)
router.get("/:id", optionalAuth, reportController.getReportById);

// Update report status (authenticated - could be admin only in future)
router.put(
  "/:id/status",
  authenticateToken,
  reportController.updateReportStatus
);

// Delete report (authenticated - user can only delete their own)
router.delete("/:id", authenticateToken, reportController.deleteReport);

module.exports = router;
