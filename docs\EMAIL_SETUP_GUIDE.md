# Email Setup Guide for CivicSafe

## Overview
The CivicSafe backend uses <PERSON><PERSON><PERSON><PERSON> with Gmail SMTP to send emails for:
- Welcome emails after registration
- Password reset emails
- System notifications

## Step 1: Gmail Account Setup

### Option A: Use Existing Gmail Account

1. **Enable 2-Factor Authentication**
   - Go to Google Account settings
   - Security → 2-Step Verification
   - Follow setup instructions

2. **Generate App Password**
   - Go to Google Account → Security
   - 2-Step Verification → App passwords
   - Select app: "Mail"
   - Select device: "Other (custom name)"
   - Enter name: "CivicSafe Server"
   - Copy the generated 16-character password

### Option B: Create Dedicated Gmail Account

1. **Create new Gmail account**
   - Go to accounts.google.com
   - Create account for business use
   - Username: `<EMAIL>`
   - Strong password

2. **Follow same 2FA and App Password steps as Option A**

## Step 2: Update Environment Variables

Update your `.env` file:

```env
# Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-16-character-app-password
EMAIL_FROM_NAME=CivicSafe Team
EMAIL_FROM_ADDRESS=<EMAIL>

# Optional: Custom SMTP settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
```

### Example with real values:
```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=abcd efgh ijkl mnop
EMAIL_FROM_NAME=CivicSafe Team
EMAIL_FROM_ADDRESS=<EMAIL>
```

## Step 3: Test Email Configuration

1. **Start the server**
   ```bash
   npm start
   ```

2. **Check console output**
   - Should see: "✅ Email service configured successfully"
   - If error: "❌ Email service not configured properly"

3. **Test with forgot password**
   ```bash
   curl -X POST http://localhost:3000/api/v1/auth/forgot-password \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>"}'
   ```

## Step 4: Email Templates

The server includes pre-built email templates:

### Welcome Email Template
```html
<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { background: #4CAF50; color: white; padding: 20px; }
        .content { padding: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to CivicSafe!</h1>
        </div>
        <div class="content">
            <p>Dear {{firstName}},</p>
            <p>Welcome to CivicSafe! Your account has been created successfully.</p>
            <p>Start reporting civic issues and making your community better.</p>
        </div>
    </div>
</body>
</html>
```

### Password Reset Email Template
```html
<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { background: #FF5722; color: white; padding: 20px; }
        .content { padding: 20px; }
        .password { background: #f5f5f5; padding: 10px; font-size: 18px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Password Reset - CivicSafe</h1>
        </div>
        <div class="content">
            <p>Your password has been reset. Here's your new password:</p>
            <div class="password">{{newPassword}}</div>
            <p>Please change this password after logging in.</p>
        </div>
    </div>
</body>
</html>
```

## Step 5: Custom Email Functions

### Send Custom Email
```javascript
const { sendEmail } = require('./utils/email.util');

// Send custom email
await sendEmail({
  to: '<EMAIL>',
  subject: 'Custom Subject',
  html: '<h1>Custom HTML content</h1>',
  text: 'Plain text version'
});
```

### Send Bulk Emails
```javascript
const { sendBulkEmail } = require('./utils/email.util');

// Send to multiple recipients
await sendBulkEmail({
  recipients: ['<EMAIL>', '<EMAIL>'],
  subject: 'Community Update',
  html: '<h1>Important community announcement</h1>'
});
```

## Step 6: Alternative SMTP Providers

### Using SendGrid
```env
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_SECURE=false
EMAIL_USER=apikey
EMAIL_PASS=your-sendgrid-api-key
```

### Using Mailgun
```env
SMTP_HOST=smtp.mailgun.org
SMTP_PORT=587
SMTP_SECURE=false
EMAIL_USER=your-mailgun-username
EMAIL_PASS=your-mailgun-password
```

### Using AWS SES
```env
SMTP_HOST=email-smtp.us-east-1.amazonaws.com
SMTP_PORT=587
SMTP_SECURE=false
EMAIL_USER=your-aws-access-key
EMAIL_PASS=your-aws-secret-key
```

## Step 7: Production Considerations

### Security Best Practices
1. **Use App Passwords**: Never use your main Gmail password
2. **Environment Variables**: Store credentials in environment variables
3. **Rate Limiting**: Gmail has sending limits (500 emails/day for free accounts)
4. **Monitoring**: Monitor email delivery rates

### Gmail Limits
- **Free Gmail**: 500 emails per day
- **Google Workspace**: 2000 emails per day
- **Rate Limit**: 100 emails per hour

### Scaling Solutions
1. **Google Workspace**: Higher limits for business accounts
2. **SendGrid**: Professional email service with high limits
3. **AWS SES**: Cost-effective for high volume
4. **Mailgun**: Developer-friendly with good APIs

## Step 8: Troubleshooting

### Common Issues

1. **"Invalid login credentials"**
   - Check if 2FA is enabled
   - Verify app password is correct (16 characters)
   - Ensure no spaces in password

2. **"Less secure app access"**
   - Gmail deprecated this - use App Passwords instead
   - Enable 2FA first, then generate App Password

3. **"Daily sending quota exceeded"**
   - Wait 24 hours or upgrade to Google Workspace
   - Consider alternative SMTP provider

4. **Emails going to spam**
   - Set up SPF, DKIM, DMARC records
   - Use professional email address
   - Avoid spam trigger words

### Debug Mode

Enable email debugging in your `.env`:
```env
NODE_ENV=development
EMAIL_DEBUG=true
```

This will show detailed SMTP logs in console.

## Step 9: Testing Email Templates

### Test Welcome Email
```bash
# Register new user to trigger welcome email
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Test",
    "lastName": "User",
    "email": "<EMAIL>",
    "mobile": "**********",
    "gender": "male",
    "password": "Password123"
  }'
```

### Test Password Reset Email
```bash
# Trigger password reset email
curl -X POST http://localhost:3000/api/v1/auth/forgot-password \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

## Step 10: Monitoring and Analytics

### Email Delivery Tracking
```javascript
// Add to email utility for tracking
const emailResult = await sendEmail(emailOptions);
if (emailResult.success) {
  console.log('Email sent successfully:', emailResult.messageId);
} else {
  console.error('Email failed:', emailResult.error);
}
```

### Database Logging
Consider logging email events:
```javascript
// Log email events to database
await EmailLog.create({
  recipient: email,
  subject: subject,
  status: 'sent',
  messageId: result.messageId,
  sentAt: new Date()
});
```

## Summary

After completing these steps:
1. ✅ Gmail account configured with App Password
2. ✅ Environment variables updated
3. ✅ Email service tested and working
4. ✅ Welcome and password reset emails functional
5. ✅ Production considerations implemented

Your CivicSafe app will now send emails for:
- User registration welcome messages
- Password reset notifications
- System announcements (if implemented)
- Custom notifications as needed
