# Country Code Implementation Guide

## Overview

The CivicSafe backend now supports international mobile numbers with country codes. This implementation allows users from different countries to register and use the platform with their local mobile number formats.

## 🔧 **Implementation Details**

### **Database Schema Changes**

#### **User Model Updates**
```javascript
// New fields added to User schema
countryCode: {
  type: String,
  required: true,
  validate: {
    validator: function (v) {
      return /^\+\d{1,4}$/.test(v);
    },
    message: "Country code must start with + and have 1-4 digits (e.g., +91, +1)",
  },
},
mobile: {
  type: String,
  required: true,
  validate: {
    validator: function (v) {
      return /^\d{6,15}$/.test(v);
    },
    message: "Mobile number must be 6-15 digits",
  },
}

// Compound unique index for countryCode + mobile combination
userSchema.index({ countryCode: 1, mobile: 1 }, { unique: true });
```

### **API Changes**

#### **Registration Endpoint**
**POST** `/api/v1/auth/register`

**Request Body:**
```json
{
  "firstName": "<PERSON>",
  "lastName": "Doe", 
  "email": "<EMAIL>",
  "countryCode": "+91",
  "mobile": "9876543210",
  "gender": "male",
  "password": "SecurePass123",
  "fcmToken": "firebase_token_here",
  "city": "Mumbai",
  "state": "Maharashtra"
}
```

#### **Profile Update Endpoint**
**PUT** `/api/v1/user/profile`

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "countryCode": "+1",
  "mobile": "5551234567",
  "gender": "male",
  "city": "New York",
  "state": "NY"
}
```

#### **Profile Response**
```json
{
  "success": true,
  "data": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "countryCode": "+91",
    "mobile": "9876543210",
    "gender": "male",
    "profilePic": "http://localhost:3000/images/profile.jpg",
    "city": "Mumbai",
    "state": "Maharashtra",
    "reportCount": 5,
    "badge": "Active Reporter",
    "totalLikesDone": 12
  }
}
```

## 🌍 **Supported Country Codes**

### **Popular Country Codes**
- **+1** - United States, Canada
- **+91** - India
- **+44** - United Kingdom
- **+86** - China
- **+81** - Japan
- **+49** - Germany
- **+33** - France
- **+61** - Australia
- **+55** - Brazil
- **+7** - Russia
- **+82** - South Korea
- **+65** - Singapore
- **+971** - UAE
- **+966** - Saudi Arabia

### **Validation Rules**
- **Country Code**: Must start with `+` followed by 1-4 digits
- **Mobile Number**: Must be 6-15 digits (international standard)
- **Full Number Validation**: Combined country code + mobile number is validated using the `validator` library

## 🔒 **Security Features**

### **Unique Constraint**
- **Compound Index**: `countryCode + mobile` combination must be unique
- **Prevents Duplicates**: Same mobile number can exist for different countries
- **Database Level**: Enforced at MongoDB level for data integrity

### **Validation Layers**
1. **Frontend Validation**: Client-side validation for immediate feedback
2. **Middleware Validation**: Express-validator for request validation
3. **Database Validation**: Mongoose schema validation
4. **Basic Format Validation**: Country code (**** digits) and mobile (6-15 digits)

## 📱 **Mobile App Integration**

### **Country Code Picker**
```javascript
// Example mobile implementation
const countryCodePicker = {
  defaultCountry: 'IN', // India
  countries: [
    { code: '+91', name: 'India', flag: '🇮🇳' },
    { code: '+1', name: 'United States', flag: '🇺🇸' },
    { code: '+44', name: 'United Kingdom', flag: '🇬🇧' },
    // ... more countries
  ]
};
```

### **Registration Flow**
1. User selects country from picker
2. Country code is auto-filled
3. User enters mobile number (digits only)
4. Full validation on submit
5. Server validates complete phone number

## 🧪 **Testing Examples**

### **Valid Registrations**
```bash
# Indian Number
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Raj",
    "lastName": "Patel",
    "email": "<EMAIL>",
    "countryCode": "+91",
    "mobile": "9876543210",
    "gender": "male",
    "password": "SecurePass123"
  }'

# US Number
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Smith",
    "email": "<EMAIL>",
    "countryCode": "+1",
    "mobile": "5551234567",
    "gender": "male",
    "password": "SecurePass123"
  }'
```

### **Error Cases**
```bash
# Invalid country code
{
  "countryCode": "91",  // Missing +
  "mobile": "9876543210"
}
# Error: "Country code must start with + and have 1-4 digits"

# Invalid mobile format
{
  "countryCode": "+91",
  "mobile": "98765"  // Too short
}
# Error: "Mobile number must be 6-15 digits"

# Duplicate number for same country
{
  "countryCode": "+91",
  "mobile": "9876543210"  // Already exists for +91
}
# Error: "Mobile number already exists for this country"

# Same number, different country (ALLOWED)
{
  "countryCode": "+1",
  "mobile": "9876543210"  // Same number, different country
}
# Success: Different country codes allow same mobile numbers
```

## 🔄 **Migration Notes**

### **Existing Data**
- **No automatic migration** - this is a v1.0 implementation
- **New registrations** will require country code
- **Clean database** recommended for production deployment

### **Database Indexes**
```javascript
// Create compound index manually if needed
db.users.createIndex({ "countryCode": 1, "mobile": 1 }, { unique: true })
```

## ✅ **Benefits**

1. **🌍 International Support**: Users from any country can register
2. **📱 Mobile-Friendly**: Standard country code picker integration
3. **🔒 Data Integrity**: Prevents duplicate numbers across countries
4. **✅ Validation**: Multiple layers of validation for data quality
5. **🚀 Scalable**: Supports future expansion to global markets
6. **📊 Analytics**: Country-wise user analytics possible

## 🎯 **Next Steps**

1. **Frontend Integration**: Implement country code picker in mobile app
2. **SMS Verification**: Add OTP verification for mobile numbers
3. **Country Analytics**: Track user distribution by country
4. **Localization**: Support for country-specific features
5. **Phone Verification**: Implement phone number verification flow

---

## 🧪 **Test Results**

### ✅ **Registration Tests**
```bash
# ✅ Indian Number Registration
POST /api/v1/auth/register
{
  "countryCode": "+91",
  "mobile": "9876543210"
}
Result: SUCCESS - User registered successfully

# ✅ US Number Registration
POST /api/v1/auth/register
{
  "countryCode": "+1",
  "mobile": "5551234567"
}
Result: SUCCESS - User registered successfully

# ✅ UK Number Registration
POST /api/v1/auth/register
{
  "countryCode": "+44",
  "mobile": "7700900123"
}
Result: SUCCESS - User registered successfully
```

### ✅ **Validation Tests**
```bash
# ✅ Invalid Country Code
{
  "countryCode": "91"  // Missing +
}
Result: ERROR - "Country code must start with + and have 1-4 digits"

# ✅ Duplicate Mobile Number (Same Country)
{
  "countryCode": "+91",
  "mobile": "9876543210"  // Already exists
}
Result: ERROR - "Mobile number already exists for this country"

# ✅ Same Mobile, Different Country (Allowed)
{
  "countryCode": "+1",
  "mobile": "9876543210"  // Same number, different country
}
Result: SUCCESS - Registration allowed for different countries
```

---

**Status**: ✅ **IMPLEMENTED AND TESTED**
- Country code validation: ✅ Working
- Mobile number validation: ✅ Working
- Compound unique constraint: ✅ Working
- Registration endpoint: ✅ Working
- Profile update endpoint: ✅ Working
- International number support: ✅ Working
- Duplicate prevention: ✅ Working
