const router = require("express").Router();
const feedController = require("../controllers/feed.controller");
const {
  authenticateToken,
  optionalAuth,
} = require("../middlewares/auth.middleware");
const {
  uploadSingle,
  cleanupOnError,
} = require("../middlewares/upload.middleware");
const {
  validateFeed,
  validateComment,
  generalLimiter,
} = require("../middlewares/security.middleware");

// Create feed post (authenticated)
router.post(
  "/",
  authenticateToken,
  generalLimiter,
  uploadSingle("featuredImage"),
  cleanupOnError,
  validateFeed,
  feedController.createFeed
);

// Get all feeds (public with optional auth for user context)
router.get("/", optionalAuth, feedController.getAllFeeds);

// Get single feed (public with optional auth)
router.get("/:id", optionalAuth, feedController.getFeedById);

// Like/Unlike feed (authenticated)
router.post("/:id/like", authenticateToken, feedController.toggleLike);

// Add comment to feed (authenticated)
router.post(
  "/:id/comment",
  authenticateToken,
  validateComment,
  feedController.addComment
);

// Delete feed (authenticated - user can only delete their own)
router.delete("/:id", authenticateToken, feedController.deleteFeed);

module.exports = router;
