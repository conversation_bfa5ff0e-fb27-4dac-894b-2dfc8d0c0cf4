# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGO_URI=mongodb://localhost:27017/civicsafe

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_complex
JWT_EXPIRES_IN=7d

# Email Configuration (Gmail)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Base URL for file serving
BASE_URL=http://localhost:5000

# Firebase Configuration (for push notifications)
# Place your Firebase service account JSON file at: config/firebaseServiceAccount.json
# Download it from Firebase Console > Project Settings > Service Accounts > Generate new private key

# Optional: Database Configuration for Production
# MONGO_URI=mongodb+srv://username:<EMAIL>/civicsafe?retryWrites=true&w=majority

# Optional: Email Configuration for other providers
# For Outlook/Hotmail:
# EMAIL_HOST=smtp-mail.outlook.com
# EMAIL_PORT=587
# EMAIL_SECURE=false

# For Yahoo:
# EMAIL_HOST=smtp.mail.yahoo.com
# EMAIL_PORT=587
# EMAIL_SECURE=false

# For custom SMTP:
# EMAIL_HOST=your-smtp-host.com
# EMAIL_PORT=587
# EMAIL_SECURE=false
# EMAIL_USER=<EMAIL>
# EMAIL_PASS=your-password
