const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const mongoose = require("mongoose");
const path = require("path");
const fs = require("fs");
require("dotenv").config();

// Import utilities
const { verifyEmailConfig } = require("./utils/email.util");
const { initializeFirebase } = require("./utils/notification.util");
const { extractTimezone } = require("./utils/timezone.util");

// Import middlewares
const { errorHandler, notFound } = require("./middlewares/error.middleware");

const app = express();
const PORT = process.env.PORT || 3000;

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, "logs");
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Security middleware
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" },
  })
);

// CORS configuration
app.use(
  cors({
    origin: true, // Allow all origins for mobile app
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Logging middleware
const accessLogStream = fs.createWriteStream(path.join(logsDir, "access.log"), {
  flags: "a",
});

app.use(morgan("combined", { stream: accessLogStream }));
app.use(morgan("dev")); // Console logging

// Timezone middleware - extract user timezone from headers
app.use(extractTimezone);

// Static file serving for images
app.use("/images", express.static(path.join(__dirname, "uploads")));

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    success: true,
    message: "CivicSafe API is running",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
  });
});

// API routes
app.use("/api/v1", require("./routes/index"));

// 404 handler
app.use(notFound);

// Global error handler
app.use(errorHandler);

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGO_URI, {});

    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error("Database connection error:", error);
    process.exit(1);
  }
};

// Initialize services
const initializeServices = async () => {
  // Verify email configuration
  const emailConfigValid = await verifyEmailConfig();
  if (!emailConfigValid) {
    console.warn(
      "Email service not configured properly. Email features will be disabled."
    );
  }

  // Initialize Firebase for push notifications
  const firebaseInitialized = initializeFirebase();
  if (!firebaseInitialized) {
    console.warn(
      "Firebase not configured properly. Push notifications will be disabled."
    );
  }
};

// Start server
const startServer = async () => {
  try {
    // Connect to database
    await connectDB();

    // Initialize services
    await initializeServices();

    // Start listening
    app.listen(PORT, () => {
      console.log(`🚀 CivicSafe Server is running on port: ${PORT}`);
      console.log(`📱 API Base URL: http://localhost:${PORT}/api/v1`);
      console.log(`🖼️  Images URL: http://localhost:${PORT}/images`);
      console.log(`💚 Health Check: http://localhost:${PORT}/health`);
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on("unhandledRejection", (err, promise) => {
  console.error("Unhandled Promise Rejection:", err.message);
  process.exit(1);
});

// Handle uncaught exceptions
process.on("uncaughtException", (err) => {
  console.error("Uncaught Exception:", err.message);
  process.exit(1);
});

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("SIGTERM received. Shutting down gracefully...");
  process.exit(0);
});

process.on("SIGINT", () => {
  console.log("SIGINT received. Shutting down gracefully...");
  process.exit(0);
});

// Start the server
startServer();
