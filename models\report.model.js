const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const issueTypes = [
  "Flooded Area",
  "Dim/No Streetlight",
  "Suspicious Activity",
  "Open Drain",
  "Road Blockage",
  "Power Outage",
  "Vandalism",
  "Illegal Dumping",
  "Broken Signal Light",
  "Fire Hazard",
  "Unsafe Crossing",
  "Loud Noise Disturbance",
  "Animal Threat",
  "Accident Spot",
  "Lost Child Alert",
  "Drug-Use Area",
  "Domestic Violence Alert",
  "Stalker Sighting",
  "Protest or Riot Zone",
  "Evacuation Notice",
];

const affectedRoles = [
  "Civilians",
  "Women & Children",
  "Elderly",
  "Commuters",
  "Cyclists",
  "School Students",
  "Night Shift Workers",
  "First Responders",
  "Delivery Personnel",
  "Volunteers",
  "Local Authorities",
  "Residents of Nearby Area",
  "Travellers or Tourists",
  "People with Disabilities",
  "Business Owners",
];

const reportSchema = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    location: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100,
    },
    description: {
      type: String,
      required: true,
      trim: true,
      maxlength: 1000,
    },
    issueType: {
      type: String,
      required: true,
      enum: issueTypes,
    },
    affectedRole: {
      type: String,
      required: true,
      enum: affectedRoles,
    },
    incidentDateTime: {
      type: Date,
      required: true,
    },
    imagePath: {
      type: String,
      default: null,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("Report", reportSchema);
