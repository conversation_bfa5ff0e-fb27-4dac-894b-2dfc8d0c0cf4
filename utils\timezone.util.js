const { DateTime } = require("luxon");

/**
 * Convert UTC date to user's timezone
 * @param {Date} utcDate - UTC date from database
 * @param {string} userTimezone - User's timezone (e.g., 'Asia/Kolkata')
 * @returns {string} - Formatted date in user's timezone
 */
const convertUTCToUserTimezone = (utcDate, userTimezone = "UTC") => {
  if (!utcDate) return null;

  try {
    const dt = DateTime.fromJSDate(new Date(utcDate), { zone: "UTC" });
    return dt.setZone(userTimezone).toFormat("yyyy-MM-dd HH:mm:ss");
  } catch (error) {
    console.error("Timezone conversion error:", error);
    return DateTime.fromJSDate(new Date(utcDate), { zone: "UTC" }).toFormat(
      "yyyy-MM-dd HH:mm:ss"
    );
  }
};

/**
 * Convert user's timezone date to UTC for database storage
 * @param {string} userDate - Date in user's timezone
 * @param {string} userTimezone - User's timezone
 * @returns {Date} - UTC date for database
 */
const convertUserTimezoneToUTC = (userDate, userTimezone = "UTC") => {
  if (!userDate) return new Date();

  try {
    const dt = DateTime.fromJSDate(new Date(userDate), { zone: userTimezone });
    return dt.toUTC().toJSDate();
  } catch (error) {
    console.error("Timezone conversion error:", error);
    return new Date(userDate);
  }
};

/**
 * Get current UTC date
 * @returns {Date} - Current UTC date
 */
const getCurrentUTC = () => {
  return new Date();
};

/**
 * Format date for API response with user timezone
 * @param {Date} utcDate - UTC date from database
 * @param {string} userTimezone - User's timezone from headers
 * @returns {Object} - Object with both UTC and user timezone
 */
const formatDateForResponse = (utcDate, userTimezone = "UTC") => {
  if (!utcDate) return null;

  return {
    utc: DateTime.fromJSDate(new Date(utcDate), { zone: "UTC" }).toFormat(
      "yyyy-MM-dd HH:mm:ss"
    ),
    userTimezone: convertUTCToUserTimezone(utcDate, userTimezone),
    timezone: userTimezone,
  };
};

/**
 * Middleware to extract timezone from headers
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
const extractTimezone = (req, res, next) => {
  // Get timezone from headers (x-user-timezone)
  const userTimezone = req.headers["x-user-timezone"] || "UTC";

  // Validate timezone
  try {
    DateTime.now().setZone(userTimezone);
    req.userTimezone = userTimezone;
  } catch (error) {
    console.warn(`Invalid timezone: ${userTimezone}, using UTC`);
    req.userTimezone = "UTC";
  }

  next();
};

module.exports = {
  convertUTCToUserTimezone,
  convertUserTimezoneToUTC,
  getCurrentUTC,
  formatDateForResponse,
  extractTimezone,
};
