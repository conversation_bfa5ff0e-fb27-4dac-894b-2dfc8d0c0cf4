const nodemailer = require('nodemailer');

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });
};

// Send email function
const sendEmail = async (to, subject, text, html = null) => {
  try {
    const transporter = createTransporter();
    
    const mailOptions = {
      from: `"CivicSafe" <${process.env.EMAIL_USER}>`,
      to: to,
      subject: subject,
      text: text,
      html: html || text
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error: error.message };
  }
};

// Send password reset email
const sendPasswordResetEmail = async (email, newPassword) => {
  const subject = 'CivicSafe - Password Reset';
  const text = `We've reset your password. Your new password is: ${newPassword}. Please log in and update your password as soon as possible.`;
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333;">Password Reset - CivicSafe</h2>
      <p>Hello,</p>
      <p>We've reset your password as requested. Your new password is:</p>
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <strong style="font-size: 18px; color: #007bff;">${newPassword}</strong>
      </div>
      <p><strong>Important:</strong> Please log in and update your password as soon as possible for security reasons.</p>
      <p>If you didn't request this password reset, please contact our support team immediately.</p>
      <hr style="margin: 30px 0;">
      <p style="color: #666; font-size: 12px;">
        This is an automated email from CivicSafe. Please do not reply to this email.
      </p>
    </div>
  `;

  return await sendEmail(email, subject, text, html);
};

// Send welcome email
const sendWelcomeEmail = async (email, firstName) => {
  const subject = 'Welcome to CivicSafe!';
  const text = `Welcome to CivicSafe, ${firstName}! Thank you for joining our community. Start reporting issues and making your city better.`;
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #007bff;">Welcome to CivicSafe!</h2>
      <p>Hello ${firstName},</p>
      <p>Thank you for joining CivicSafe! We're excited to have you as part of our community.</p>
      <p>With CivicSafe, you can:</p>
      <ul>
        <li>Report civic issues in your area</li>
        <li>Stay updated with community feeds</li>
        <li>Connect with fellow citizens</li>
        <li>Make your city a better place</li>
      </ul>
      <p>Start exploring and making a difference today!</p>
      <hr style="margin: 30px 0;">
      <p style="color: #666; font-size: 12px;">
        This is an automated email from CivicSafe. Please do not reply to this email.
      </p>
    </div>
  `;

  return await sendEmail(email, subject, text, html);
};

// Verify email configuration
const verifyEmailConfig = async () => {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    console.log('Email configuration verified successfully');
    return true;
  } catch (error) {
    console.error('Email configuration error:', error);
    return false;
  }
};

module.exports = {
  sendEmail,
  sendPasswordResetEmail,
  sendWelcomeEmail,
  verifyEmailConfig
};
