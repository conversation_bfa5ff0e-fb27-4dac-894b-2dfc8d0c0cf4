# Firebase Push Notifications Setup Guide

## Step 1: Create Firebase Project

1. **Go to Firebase Console**
   - Visit: https://console.firebase.google.com/
   - Click "Create a project" or "Add project"

2. **Project Setup**
   - Enter project name: `civicsafe-app`
   - Enable Google Analytics (optional)
   - Choose analytics account or create new one
   - Click "Create project"

## Step 2: Add Android App (if applicable)

1. **Register Android App**
   - Click "Add app" → Android icon
   - Enter package name: `com.civicsafe.app` (or your package name)
   - Enter app nickname: `CivicSafe Android`
   - Enter SHA-1 certificate (optional for development)

2. **Download Configuration**
   - Download `google-services.json`
   - Place it in your Android app's `app/` directory

## Step 3: Add iOS App (if applicable)

1. **Register iOS App**
   - Click "Add app" → iOS icon
   - Enter bundle ID: `com.civicsafe.app` (or your bundle ID)
   - Enter app nickname: `CivicSafe iOS`

2. **Download Configuration**
   - Download `GoogleService-Info.plist`
   - Add it to your iOS project in Xcode

## Step 4: Generate Service Account Key

1. **Go to Project Settings**
   - Click gear icon → "Project settings"
   - Go to "Service accounts" tab

2. **Generate Private Key**
   - Click "Generate new private key"
   - Choose "Node.js" configuration
   - Click "Generate key"
   - Save the JSON file as `firebaseServiceAccount.json`

3. **Place Service Account File**
   ```bash
   # In your server project
   mkdir -p config
   mv /path/to/downloaded/file.json config/firebaseServiceAccount.json
   ```

## Step 5: Update Environment Variables

Add to your `.env` file:
```env
# Firebase Configuration (optional - will use service account file)
FIREBASE_PROJECT_ID=civicsafe-app
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY\n-----END PRIVATE KEY-----\n"
```

## Step 6: Test Firebase Setup

1. **Start your server**
   ```bash
   npm start
   ```

2. **Check console output**
   - Should see: "✅ Firebase initialized successfully"
   - If error: "❌ Firebase not configured properly"

3. **Test notification endpoint** (after user registration)
   ```bash
   curl -X POST http://localhost:3000/api/v1/test-notification \
     -H "Content-Type: application/json" \
     -d '{"userId": "USER_ID", "message": "Test notification"}'
   ```

## Step 7: Mobile App Integration

### Android Setup (React Native)

1. **Install dependencies**
   ```bash
   npm install @react-native-firebase/app @react-native-firebase/messaging
   ```

2. **Get FCM Token**
   ```javascript
   import messaging from '@react-native-firebase/messaging';
   
   const getFCMToken = async () => {
     try {
       const token = await messaging().getToken();
       console.log('FCM Token:', token);
       return token;
     } catch (error) {
       console.error('Error getting FCM token:', error);
     }
   };
   ```

3. **Send token to server during login/register**
   ```javascript
   const loginUser = async (email, password) => {
     const fcmToken = await getFCMToken();
     
     const response = await fetch('/api/v1/auth/login', {
       method: 'POST',
       headers: { 'Content-Type': 'application/json' },
       body: JSON.stringify({
         email,
         password,
         fcmToken // Send FCM token
       })
     });
   };
   ```

### iOS Setup (React Native)

1. **Request permission**
   ```javascript
   import messaging from '@react-native-firebase/messaging';
   
   const requestUserPermission = async () => {
     const authStatus = await messaging().requestPermission();
     const enabled =
       authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
       authStatus === messaging.AuthorizationStatus.PROVISIONAL;
   
     if (enabled) {
       console.log('Authorization status:', authStatus);
       return true;
     }
     return false;
   };
   ```

## Step 8: Server-Side Notification Functions

The server already includes notification utilities. Here's how they work:

### Send Notification to User
```javascript
const { sendNotificationToUser } = require('./utils/notification.util');

// Send notification to specific user
await sendNotificationToUser(userId, {
  title: 'New Report in Your Area',
  body: 'A new infrastructure issue was reported near you',
  data: {
    type: 'new_report',
    reportId: 'report_id_here'
  }
});
```

### Send Notification to City
```javascript
const { sendNotificationToCity } = require('./utils/notification.util');

// Send notification to all users in a city
await sendNotificationToCity('Mumbai', {
  title: 'City Update',
  body: 'New community announcement',
  data: {
    type: 'announcement',
    feedId: 'feed_id_here'
  }
});
```

## Step 9: Testing Notifications

### Test with Postman/cURL

1. **Register a user with FCM token**
   ```bash
   curl -X POST http://localhost:3000/api/v1/auth/register \
     -H "Content-Type: application/json" \
     -d '{
       "firstName": "Test",
       "lastName": "User",
       "email": "<EMAIL>",
       "mobile": "9876543210",
       "gender": "male",
       "password": "Password123",
       "fcmToken": "YOUR_FCM_TOKEN_FROM_MOBILE_APP"
     }'
   ```

2. **Create a report (triggers city notification)**
   ```bash
   curl -X POST http://localhost:3000/api/v1/reports \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "location": "Mumbai",
       "description": "Test report",
       "issueType": "infrastructure",
       "affectedRole": "citizen"
     }'
   ```

## Step 10: Troubleshooting

### Common Issues

1. **"Firebase not configured properly"**
   - Check if `config/firebaseServiceAccount.json` exists
   - Verify JSON file format and permissions
   - Ensure project ID matches

2. **"Invalid FCM token"**
   - Token might be expired or invalid
   - Regenerate token on mobile app
   - Check token format (should be long string)

3. **Notifications not received**
   - Check mobile app permissions
   - Verify FCM token is correctly sent to server
   - Check Firebase console for delivery status

### Debug Mode

Enable debug logging in your `.env`:
```env
NODE_ENV=development
```

This will show detailed Firebase error messages in console.

## Step 11: Production Considerations

1. **Security Rules**
   - Restrict Firebase console access
   - Use environment variables for sensitive data
   - Rotate service account keys regularly

2. **Monitoring**
   - Monitor notification delivery rates in Firebase console
   - Set up alerts for failed notifications
   - Track user engagement metrics

3. **Scaling**
   - Consider using Firebase Cloud Functions for complex logic
   - Implement notification queuing for high volume
   - Use topic-based messaging for broadcast notifications

## Summary

After completing these steps:
1. ✅ Firebase project created
2. ✅ Service account key generated and placed
3. ✅ Mobile apps configured with FCM
4. ✅ Server can send notifications
5. ✅ Users receive push notifications

Your CivicSafe app will now support push notifications for:
- New reports in user's city
- Feed interactions (likes, comments)
- System announcements
- Password reset confirmations
