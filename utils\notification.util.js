const admin = require("firebase-admin");
const User = require("../models/users.model");
const path = require("path");

// Initialize Firebase Admin SDK
let firebaseApp = null;

const initializeFirebase = () => {
  try {
    if (!firebaseApp) {
      const serviceAccountPath = path.join(
        __dirname,
        "../config/firebaseServiceAccount.json"
      );

      // Check if service account file exists
      const fs = require("fs");
      if (!fs.existsSync(serviceAccountPath)) {
        console.warn(
          "Firebase service account file not found. Push notifications will be disabled."
        );
        return false;
      }

      const serviceAccount = require(serviceAccountPath);

      firebaseApp = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });

      console.log("Firebase Admin SDK initialized successfully");
      return true;
    }
    return true;
  } catch (error) {
    console.error("Error initializing Firebase:", error);
    return false;
  }
};

// Send notification to a single user
const sendNotificationToUser = async (userId, title, body, data = {}) => {
  try {
    if (!firebaseApp && !initializeFirebase()) {
      console.warn("Firebase not initialized. Skipping notification.");
      return { success: false, error: "Firebase not initialized" };
    }

    const user = await User.findById(userId);
    if (!user || !user.fcmToken) {
      return { success: false, error: "User not found or no FCM token" };
    }

    const message = {
      notification: {
        title: title,
        body: body,
      },
      data: {
        ...data,
        timestamp: Date.now().toString(),
      },
      token: user.fcmToken,
    };

    const response = await admin.messaging().send(message);
    console.log("Notification sent successfully:", response);
    return { success: true, messageId: response };
  } catch (error) {
    console.error("Error sending notification:", error);

    // If token is invalid, remove it from user
    if (
      error.code === "messaging/invalid-registration-token" ||
      error.code === "messaging/registration-token-not-registered"
    ) {
      await User.findByIdAndUpdate(userId, { $unset: { fcmToken: 1 } });
    }

    return { success: false, error: error.message };
  }
};

// Send notification to multiple users
const sendNotificationToUsers = async (userIds, title, body, data = {}) => {
  try {
    if (!firebaseApp && !initializeFirebase()) {
      console.warn("Firebase not initialized. Skipping notifications.");
      return { success: false, error: "Firebase not initialized" };
    }

    const users = await User.find({
      _id: { $in: userIds },
      fcmToken: { $exists: true, $ne: null },
    });

    if (users.length === 0) {
      return { success: false, error: "No users with valid FCM tokens found" };
    }

    const tokens = users.map((user) => user.fcmToken);

    const message = {
      notification: {
        title: title,
        body: body,
      },
      data: {
        ...data,
        timestamp: Date.now().toString(),
      },
      tokens: tokens,
    };

    // Check if Firebase is properly initialized
    if (!firebaseApp) {
      console.warn("Firebase not initialized. Skipping notification.");
      return { successCount: 0, failureCount: tokens.length };
    }

    console.log(`Attempting to send notifications to ${tokens.length} tokens`);
    console.log("Available messaging methods:", {
      send: typeof admin.messaging().send,
      sendEachForMulticast: typeof admin.messaging().sendEachForMulticast,
      sendMulticast: typeof admin.messaging().sendMulticast
    });

    // Try different methods based on Firebase Admin SDK version
    let response;
    try {
      // Try sendEachForMulticast first (newer versions)
      if (admin.messaging().sendEachForMulticast) {
        response = await admin.messaging().sendEachForMulticast(message);
      } else if (admin.messaging().sendMulticast) {
        // Fallback to sendMulticast (older versions)
        response = await admin.messaging().sendMulticast(message);
      } else {
        // If neither method exists, send individual messages
        console.warn("Multicast methods not available, sending individual messages");
        const responses = [];
        for (const token of tokens) {
          try {
            const singleMessage = {
              notification: message.notification,
              data: message.data,
              token: token,
            };
            const singleResponse = await admin.messaging().send(singleMessage);
            responses.push({ success: true, messageId: singleResponse });
          } catch (error) {
            responses.push({ success: false, error: error });
          }
        }

        const successCount = responses.filter(r => r.success).length;
        const failureCount = responses.filter(r => !r.success).length;

        response = {
          successCount,
          failureCount,
          responses: responses.map(r => ({ success: r.success }))
        };
      }
    } catch (error) {
      console.error("Error with multicast messaging:", error);
      throw error;
    }

    // Handle failed tokens
    if (response.failureCount > 0) {
      const failedTokens = [];
      response.responses.forEach((resp, idx) => {
        if (!resp.success) {
          failedTokens.push(tokens[idx]);
          // Remove invalid tokens
          if (
            resp.error.code === "messaging/invalid-registration-token" ||
            resp.error.code === "messaging/registration-token-not-registered"
          ) {
            User.findOneAndUpdate(
              { fcmToken: tokens[idx] },
              { $unset: { fcmToken: 1 } }
            ).exec();
          }
        }
      });
      console.log("Failed tokens removed:", failedTokens.length);
    }

    console.log(
      `Notifications sent: ${response.successCount}/${tokens.length}`
    );
    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount,
    };
  } catch (error) {
    console.error("Error sending notifications:", error);
    return { success: false, error: error.message };
  }
};

// Notify users by city when a new report is created
const notifyUsersByCity = async (city, payload) => {
  try {
    if (!firebaseApp && !initializeFirebase()) {
      console.warn("Firebase not initialized. Skipping city notifications.");
      return { success: false, error: "Firebase not initialized" };
    }

    // Find users in the same city with FCM tokens
    // Use case-insensitive search for city matching
    const users = await User.find({
      fcmToken: { $exists: true, $ne: null },
      city: { $regex: new RegExp(`^${city.trim()}$`, 'i') }
    }).limit(100); // Limit to prevent spam

    if (users.length === 0) {
      console.log(`No users found in city: ${city} with FCM tokens`);
      return { success: false, error: `No users with FCM tokens found in ${city}` };
    }

    console.log(`Found ${users.length} users in ${city} to notify`);

    const title = `New Issue Reported in ${city}`;
    const body = `A new ${payload.issueType} issue has been reported. Stay informed about your community.`;

    const userIds = users.map((user) => user._id);

    return await sendNotificationToUsers(userIds, title, body, {
      type: "new_report",
      city: city,
      issueType: payload.issueType,
      reportId: payload.reportId,
    });
  } catch (error) {
    console.error("Error notifying users by city:", error);
    return { success: false, error: error.message };
  }
};

// Update user's FCM token
const updateUserFCMToken = async (userId, fcmToken) => {
  try {
    await User.findByIdAndUpdate(userId, { fcmToken: fcmToken });
    return { success: true };
  } catch (error) {
    console.error("Error updating FCM token:", error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  initializeFirebase,
  sendNotificationToUser,
  sendNotificationToUsers,
  notifyUsersByCity,
  updateUserFCMToken,
};
