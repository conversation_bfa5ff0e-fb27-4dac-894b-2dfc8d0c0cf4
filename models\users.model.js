const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const userSchema = new Schema(
  {
    firstName: {
      type: String,
      required: true,
      trim: true,
      minlength: 2,
      maxlength: 50,
    },
    lastName: {
      type: String,
      required: true,
      trim: true,
      minlength: 2,
      maxlength: 50,
    },
    email: {
      type: String,
      required: true,
      unique: true,
    },
    countryCode: {
      type: String,
      required: true,
      validate: {
        validator: function (v) {
          // Country code should start with + and have 1-4 digits
          return /^\+\d{1,4}$/.test(v);
        },
        message:
          "Country code must start with + and have 1-4 digits (e.g., +91, +1)",
      },
    },
    mobile: {
      type: String,
      required: true,
      validate: {
        validator: function (v) {
          // Mobile number should be 6-15 digits (international standard)
          return /^\d{6,15}$/.test(v);
        },
        message: "Mobile number must be 6-15 digits",
      },
    },
    gender: {
      type: String,
      required: true,
      enum: ["male", "female", "other"],
      lowercase: true,
    },
    password: {
      type: String,
      required: true,
      minlength: 6,
    },
    fcmToken: {
      type: String,
      default: null,
    },
    profilePic: {
      type: String,
      default: null,
    },
    city: {
      type: String,
      required: false,
      trim: true,
      maxlength: 100,
      lowercase: true,
    },
    state: {
      type: String,
      required: false,
      trim: true,
      maxlength: 100,
      lowercase: true,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("User", userSchema);
