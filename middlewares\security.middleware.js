const rateLimit = require("express-rate-limit");
const { body, validationResult } = require("express-validator");

// Rate limiting configurations
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 requests per windowMs for auth routes
  message: {
    success: false,
    message: "Too many authentication attempts, please try again later",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs for general routes
  message: {
    success: false,
    message: "Too many requests, please try again later",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const forgotPasswordLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 forgot password requests per hour
  message: {
    success: false,
    message: "Too many password reset attempts, please try again later",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: "Validation failed",
      errors: errors.array(),
    });
  }
  next();
};

// User registration validation
const validateRegister = [
  body("firstName")
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage("First name must be between 2 and 50 characters"),
  body("lastName")
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage("Last name must be between 2 and 50 characters"),
  // body("email")
  //   .isEmail()
  //   .normalizeEmail()
  //   .withMessage("Please provide a valid email"),
  body("countryCode")
    .matches(/^\+\d{1,4}$/)
    .withMessage(
      "Country code must start with + and have 1-4 digits (e.g., +91, +1)"
    ),
  body("mobile")
    .matches(/^\d{6,15}$/)
    .withMessage("Mobile number must be 6-15 digits"),
  body("gender")
    .isIn(["male", "female", "other"])
    .withMessage("Gender must be male, female, or other"),
  body("password")
    .isLength({ min: 6 })
    .withMessage("Password must be at least 6 characters long")
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage(
      "Password must contain at least one uppercase letter, one lowercase letter, and one number"
    ),
  body("city")
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("City must be between 2 and 100 characters"),
  body("state")
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("State must be between 2 and 100 characters"),
  handleValidationErrors,
];

// User login validation
const validateLogin = [
  // body("email")
  //   .isEmail()
  //   .normalizeEmail()
  //   .withMessage("Please provide a valid email"),
  body("password").notEmpty().withMessage("Password is required"),
  handleValidationErrors,
];

// Forgot password validation
const validateForgotPassword = [
  body("email")
    .isEmail()
    .normalizeEmail()
    .withMessage("Please provide a valid email"),
  handleValidationErrors,
];

// Report validation
const validateReport = [
  body("location")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Location must be between 2 and 100 characters"),
  body("description")
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage("Description must be between 10 and 1000 characters"),
  // body("issueType")
  //   .isIn([
  //     "infrastructure",
  //     "safety",
  //     "environment",
  //     "traffic",
  //     "public_services",
  //     "corruption",
  //     "other",
  //   ])
  //   .withMessage("Invalid issue type"),
  // body("affectedRole")
  //   .isIn([
  //     "citizen",
  //     "student",
  //     "senior_citizen",
  //     "disabled_person",
  //     "business_owner",
  //     "tourist",
  //     "other",
  //   ])
  //   .withMessage("Invalid affected role"),
  body("incidentDateTime")
    .isISO8601()
    .withMessage("Please provide a valid ISO 8601 datetime"),
  handleValidationErrors,
];

// Feed validation
const validateFeed = [
  body("title")
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage("Title must be between 5 and 200 characters"),
  body("description")
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage("Description must be between 10 and 2000 characters"),
  body("category")
    .isIn([
      "announcement",
      "event",
      "discussion",
      "news",
      "alert",
      "community",
      "other",
    ])
    .withMessage("Invalid category"),
  handleValidationErrors,
];

// Comment validation
const validateComment = [
  body("comment")
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage("Comment must be between 1 and 500 characters"),
  handleValidationErrors,
];

// Profile update validation
const validateProfileUpdate = [
  body("firstName")
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage("First name must be between 2 and 50 characters"),
  body("lastName")
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage("Last name must be between 2 and 50 characters"),
  body("countryCode")
    .optional()
    .matches(/^\+\d{1,4}$/)
    .withMessage(
      "Country code must start with + and have 1-4 digits (e.g., +91, +1)"
    ),
  body("mobile")
    .optional()
    .matches(/^\d{6,15}$/)
    .withMessage("Mobile number must be 6-15 digits"),
  body("gender")
    .optional()
    .isIn(["male", "female", "other"])
    .withMessage("Gender must be male, female, or other"),
  body("city")
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("City must be between 2 and 100 characters"),
  body("state")
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("State must be between 2 and 100 characters"),
  handleValidationErrors,
];

module.exports = {
  authLimiter,
  generalLimiter,
  forgotPasswordLimiter,
  validateRegister,
  validateLogin,
  validateForgotPassword,
  validateReport,
  validateFeed,
  validateComment,
  validateProfileUpdate,
  handleValidationErrors,
};
