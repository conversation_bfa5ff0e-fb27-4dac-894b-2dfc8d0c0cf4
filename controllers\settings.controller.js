// Get privacy policy
const getPrivacyPolicy = async (req, res) => {
  try {
    const privacyPolicy = `
# Privacy Policy for CivicSafe

**Last updated: ${new Date().toLocaleDateString()}**

## Introduction

Welcome to CivicSafe. We are committed to protecting your personal information and your right to privacy. This Privacy Policy explains how we collect, use, and share information about you when you use our mobile application and services.

## Information We Collect

### Personal Information
- Name (first and last name)
- Email address
- Mobile phone number
- Gender
- Profile picture (optional)

### Usage Information
- Reports you submit
- Feed posts and interactions
- Comments and likes
- Location data (city names for reports)

### Device Information
- Device identifiers for push notifications
- App usage analytics

## How We Use Your Information

We use the information we collect to:
- Provide and maintain our services
- Process and respond to your reports
- Send you notifications about community issues
- Improve our services and user experience
- Communicate with you about your account

## Information Sharing

We do not sell, trade, or otherwise transfer your personal information to third parties except:
- With your consent
- To comply with legal obligations
- To protect our rights and safety

## Data Security

We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.

## Your Rights

You have the right to:
- Access your personal information
- Update or correct your information
- Delete your account and associated data
- Opt-out of certain communications

## Contact Us

If you have any questions about this Privacy Policy, please contact us at:
- Email: <EMAIL>
- Phone: +91-XXXXXXXXXX

## Changes to This Policy

We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page.
    `;

    res.status(200).json({
      success: true,
      data: {
        content: privacyPolicy.trim(),
        lastUpdated: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Get privacy policy error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch privacy policy",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Get about us
const getAboutUs = async (req, res) => {
  try {
    const aboutUs = `
# About CivicSafe

## Our Mission

CivicSafe is a community-driven platform that empowers citizens to report civic issues, stay informed about their community, and work together to make their cities better places to live.

## What We Do

### Report Issues
Citizens can easily report various civic issues including:
- Infrastructure problems (roads, bridges, public facilities)
- Safety concerns
- Environmental issues
- Traffic problems
- Public service issues
- Corruption and transparency concerns

### Community Engagement
- Share updates and announcements
- Participate in community discussions
- Stay informed about local events
- Connect with fellow citizens

### Real-time Notifications
Get instant notifications about:
- New issues reported in your area
- Updates on issues you've reported
- Community announcements
- Emergency alerts

## Our Values

### Transparency
We believe in open and transparent governance. Every report and update is visible to the community.

### Accountability
We help hold public officials and services accountable by providing a platform for citizen feedback.

### Community
We foster a sense of community by connecting citizens who care about their neighborhoods.

### Empowerment
We empower every citizen to be an agent of positive change in their community.

## How It Works

1. **Report**: Spot an issue? Report it with photos and details
2. **Track**: Follow the progress of your reports and others
3. **Engage**: Participate in community discussions
4. **Impact**: See real changes happen in your community

## Recognition System

We recognize active community members with badges:
- **Bronze Badge**: 10-24 reports
- **Silver Badge**: 25-49 reports
- **Gold Badge**: 50-99 reports
- **Diamond Badge**: 100+ reports

## Contact Us

Have questions or suggestions? We'd love to hear from you:
- Email: <EMAIL>
- Phone: +91-XXXXXXXXXX
- Website: www.civicsafe.com

## Join the Movement

Download CivicSafe today and become part of a community that's making a difference, one report at a time.

Together, we can build safer, better communities for everyone.
    `;

    res.status(200).json({
      success: true,
      data: {
        content: aboutUs.trim(),
        version: "1.0.0",
        lastUpdated: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Get about us error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch about us information",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

module.exports = {
  getPrivacyPolicy,
  getAboutUs,
};
