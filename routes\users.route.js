const router = require("express").Router();
const usersController = require("../controllers/users.controller");
const { authenticateToken } = require("../middlewares/auth.middleware");
const {
  uploadSingle,
  cleanupOnError,
} = require("../middlewares/upload.middleware");
const { validateProfileUpdate } = require("../middlewares/security.middleware");

// All user routes require authentication
router.use(authenticateToken);

// Profile routes
router.get("/profile", usersController.getUserProfile);
router.put(
  "/profile",
  uploadSingle("profilePic"),
  cleanupOnError,
  validateProfileUpdate,
  usersController.updateUserProfile
);

// User's reports and upvotes
router.get("/reports", usersController.getUserReports);
router.get("/likes", usersController.getUserLikes);

module.exports = router;
