::1 - - [02/Aug/2025:10:41:12 +0000] "GET /health HTTP/1.1" 200 110 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:10:41:54 +0000] "GET /api/v1/settings/privacy-policy HTTP/1.1" 200 1996 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:10:42:05 +0000] "GET /api/v1/settings/about-us HTTP/1.1" 200 2277 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:10:42:18 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 841 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:10:42:29 +0000] "GET /api/v1/user/profile HTTP/1.1" 401 54 "-" "curl/8.8.0"
::ffff:127.0.0.1 - - [02/Aug/2025:11:04:17 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 528 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:11:09:10 +0000] "POST /api/v1/auth/login HTTP/1.1" 200 516 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:11:19:45 +0000] "POST /api/v1/auth//forgot-password HTTP/1.1" 500 1127 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:11:19:54 +0000] "POST /api/v1/auth/forgot-password HTTP/1.1" 404 60 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:11:20:29 +0000] "POST /api/v1/auth/forgot-password HTTP/1.1" 404 60 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:11:21:03 +0000] "POST /api/v1/auth/forgot-password HTTP/1.1" 400 158 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:11:21:09 +0000] "POST /api/v1/auth/forgot-password HTTP/1.1" 500 65 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:11:23:07 +0000] "GET /api/v1/user/profile HTTP/1.1" 401 54 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:11:23:50 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 528 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:11:23:54 +0000] "POST /api/v1/auth/login HTTP/1.1" 200 516 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:11:24:49 +0000] "GET /api/v1/user/profile HTTP/1.1" 200 256 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:11:35:09 +0000] "GET /api/v1/user/profile HTTP/1.1" 401 43 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [02/Aug/2025:11:36:10 +0000] "GET /api/v1/user/profile HTTP/1.1" 200 256 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [02/Aug/2025:11:37:57 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 356 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [02/Aug/2025:11:38:14 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 185 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [02/Aug/2025:11:38:25 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 521 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [02/Aug/2025:11:40:29 +0000] "PUT /api/v1/user/profile HTTP/1.1" 500 138 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:11:41:01 +0000] "PUT /api/v1/user/profile HTTP/1.1" 200 340 "-" "PostmanRuntime/7.44.1"
::1 - - [02/Aug/2025:11:49:45 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 520 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:11:50:43 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 50 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:11:50:56 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 521 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:11:52:15 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 526 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:11:53:19 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 530 "-" "curl/8.8.0"
::ffff:127.0.0.1 - - [02/Aug/2025:11:57:55 +0000] "POST /api/v1/feeds HTTP/1.1" 401 43 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:11:58:12 +0000] "POST /api/v1/auth/login HTTP/1.1" 200 515 "-" "PostmanRuntime/7.44.1"
::1 - - [02/Aug/2025:11:58:28 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 521 "-" "curl/8.8.0"
::ffff:127.0.0.1 - - [02/Aug/2025:11:59:04 +0000] "POST /api/v1/feeds HTTP/1.1" 400 374 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:12:00:16 +0000] "POST /api/v1/feeds HTTP/1.1" 400 160 "-" "PostmanRuntime/7.44.1"
::1 - - [02/Aug/2025:12:00:18 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 655 "-" "curl/8.8.0"
::ffff:127.0.0.1 - - [02/Aug/2025:12:00:35 +0000] "POST /api/v1/feeds HTTP/1.1" 201 563 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:12:01:16 +0000] "POST /api/v1/feeds HTTP/1.1" 400 67 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:12:01:23 +0000] "POST /api/v1/feeds HTTP/1.1" 400 65 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:12:02:01 +0000] "POST /api/v1/feeds HTTP/1.1" 400 67 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:12:02:07 +0000] "POST /api/v1/feeds HTTP/1.1" 400 65 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:12:03:20 +0000] "POST /api/v1/feeds HTTP/1.1" 400 374 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:12:04:07 +0000] "POST /api/v1/feeds HTTP/1.1" 400 191 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:12:04:15 +0000] "POST /api/v1/feeds HTTP/1.1" 201 677 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:12:05:42 +0000] "GET /api/v1/feeds HTTP/1.1" 200 1284 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:12:07:40 +0000] "GET /images HTTP/1.1" 301 156 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [02/Aug/2025:12:07:40 +0000] "GET /images/ HTTP/1.1" 500 1089 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [02/Aug/2025:12:07:40 +0000] "GET /favicon.ico HTTP/1.1" 500 1127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [02/Aug/2025:12:07:54 +0000] "GET /images/ HTTP/1.1" 500 1089 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [02/Aug/2025:12:07:54 +0000] "GET /favicon.ico HTTP/1.1" 500 1127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [02/Aug/2025:12:08:04 +0000] "GET /images/ HTTP/1.1" 500 1089 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [02/Aug/2025:12:08:05 +0000] "GET /favicon.ico HTTP/1.1" 500 1127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [02/Aug/2025:12:08:13 +0000] "GET /images HTTP/1.1" 301 156 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [02/Aug/2025:12:08:13 +0000] "GET /images/ HTTP/1.1" 500 1089 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [02/Aug/2025:12:08:13 +0000] "GET /favicon.ico HTTP/1.1" 500 1127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::1 - - [02/Aug/2025:12:09:22 +0000] "HEAD /images/image-1754136255730-232300790.png HTTP/1.1" 200 2311076 "-" "curl/8.8.0"
::ffff:127.0.0.1 - - [02/Aug/2025:12:09:26 +0000] "GET /images/image-1754136255730-232300790.png HTTP/1.1" 200 2311076 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [02/Aug/2025:12:09:26 +0000] "GET /favicon.ico HTTP/1.1" 500 1127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::1 - - [02/Aug/2025:12:09:30 +0000] "GET /images/ HTTP/1.1" 500 1089 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:07:17 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 698 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:07:34 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 695 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:07:58 +0000] "POST /api/v1/reports HTTP/1.1" 400 435 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:08:37 +0000] "POST /api/v1/reports HTTP/1.1" 201 580 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:09:06 +0000] "GET /health HTTP/1.1" 200 110 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:15:10 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 58 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:15:18 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 688 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:15:29 +0000] "POST /api/v1/reports HTTP/1.1" 201 751 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:15:39 +0000] "GET /api/v1/reports?limit=5 HTTP/1.1" 200 1455 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:15:48 +0000] "GET /api/v1/reports/688e0f7169f4b5b426f61477 HTTP/1.1" 200 733 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:20:11 +0000] "POST /api/v1/reports HTTP/1.1" 201 739 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:20:24 +0000] "POST /api/v1/reports HTTP/1.1" 201 885 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:20:35 +0000] "POST /api/v1/reports HTTP/1.1" 400 374 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:20:45 +0000] "GET /api/v1/reports?limit=3 HTTP/1.1" 200 2196 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:25:10 +0000] "POST /api/v1/reports HTTP/1.1" 201 742 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:25:22 +0000] "POST /api/v1/reports HTTP/1.1" 400 168 "-" "curl/8.8.0"
::ffff:127.0.0.1 - - [02/Aug/2025:13:25:31 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 50 "-" "PostmanRuntime/7.44.1"
::1 - - [02/Aug/2025:13:25:34 +0000] "POST /api/v1/reports HTTP/1.1" 400 168 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:25:43 +0000] "GET /api/v1/reports?limit=2 HTTP/1.1" 200 1488 "-" "curl/8.8.0"
::ffff:127.0.0.1 - - [02/Aug/2025:13:27:04 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 58 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [02/Aug/2025:13:27:30 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 632 "-" "PostmanRuntime/7.45.0"
::1 - - [02/Aug/2025:13:36:50 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 659 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:36:58 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 341 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:37:07 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 664 "-" "curl/8.8.0"
::ffff:127.0.0.1 - - [02/Aug/2025:13:37:08 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 701 "-" "PostmanRuntime/7.44.1"
::1 - - [02/Aug/2025:13:37:14 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 75 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:37:28 +0000] "POST /api/v1/auth/register HTTP/1.1" 429 86 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:37:37 +0000] "POST /api/v1/auth/register HTTP/1.1" 429 86 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:37:46 +0000] "POST /api/v1/auth/login HTTP/1.1" 429 86 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:38:46 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 665 "-" "curl/8.8.0"
::ffff:127.0.0.1 - - [02/Aug/2025:13:38:55 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 50 "-" "PostmanRuntime/7.44.1"
::1 - - [02/Aug/2025:13:38:55 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 202 "-" "curl/8.8.0"
::ffff:127.0.0.1 - - [02/Aug/2025:13:39:04 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 766 "-" "PostmanRuntime/7.44.1"
::1 - - [02/Aug/2025:13:39:07 +0000] "POST /api/v1/auth/login HTTP/1.1" 401 55 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:39:19 +0000] "POST /api/v1/auth/login HTTP/1.1" 429 86 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:39:30 +0000] "GET /api/v1/user/profile HTTP/1.1" 401 44 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:39:38 +0000] "GET /sw.js HTTP/1.1" 500 1115 "http://localhost:3000/sw.js" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::1 - - [02/Aug/2025:13:39:38 +0000] "GET /sw.js HTTP/1.1" 500 1115 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::1 - - [02/Aug/2025:13:39:40 +0000] "POST /api/v1/auth/register HTTP/1.1" 429 86 "-" "curl/8.8.0"
::1 - - [02/Aug/2025:13:39:42 +0000] "GET /images/profilePic-1754141943976-472779282.png HTTP/1.1" 200 2311076 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::1 - - [02/Aug/2025:13:39:42 +0000] "GET /favicon.ico HTTP/1.1" 500 1127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::1 - - [02/Aug/2025:13:39:44 +0000] "GET /sw.js HTTP/1.1" 500 1115 "http://localhost:3000/sw.js" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::1 - - [02/Aug/2025:13:39:44 +0000] "GET /sw.js HTTP/1.1" 500 1115 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [02/Aug/2025:13:47:05 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 766 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:13:51:14 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 50 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:22:05 +0000] "GET /api/v1/user/profile HTTP/1.1" 401 43 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [02/Aug/2025:14:26:54 +0000] "GET /api/v1/feeds HTTP/1.1" 500 115 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [02/Aug/2025:14:28:05 +0000] "GET /api/v1/feeds HTTP/1.1" 500 115 "-" "WhatsApp/2.2530.2 W"
::ffff:127.0.0.1 - - [02/Aug/2025:14:28:24 +0000] "GET /api/v1/feeds HTTP/1.1" 500 115 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:28:30 +0000] "GET /favicon.ico HTTP/1.1" 500 1127 "https://rm3vmd78-3000.inc1.devtunnels.ms/api/v1/feeds" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [02/Aug/2025:14:29:33 +0000] "GET /api/v1/feeds HTTP/1.1" 200 1108 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:29:38 +0000] "GET /api/v1/feeds HTTP/1.1" 200 1108 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:29:38 +0000] "GET /api/v1/feeds HTTP/1.1" 200 1108 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [02/Aug/2025:14:37:02 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 78 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [02/Aug/2025:14:37:40 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 765 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:41:11 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 78 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [02/Aug/2025:14:43:03 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 766 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:48:05 +0000] "GET /api/v1/reports HTTP/1.1" 200 2836 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:48:24 +0000] "POST /api/v1/reports HTTP/1.1" 401 44 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:48:34 +0000] "POST /api/v1/auth/login HTTP/1.1" 200 865 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:48:49 +0000] "POST /api/v1/reports HTTP/1.1" 201 787 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:49:14 +0000] "POST /api/v1/reports HTTP/1.1" 201 790 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:51:57 +0000] "POST /api/v1/reports HTTP/1.1" 201 790 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:53:31 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 50 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:53:36 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 765 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:54:30 +0000] "POST /api/v1/reports HTTP/1.1" 201 790 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:54:42 +0000] "POST /api/v1/reports HTTP/1.1" 201 790 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:14:58:05 +0000] "POST /api/v1/reports HTTP/1.1" 201 790 "-" "PostmanRuntime/7.44.1"
::ffff:127.0.0.1 - - [02/Aug/2025:15:08:16 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 78 "-" "okhttp/5.1.0"
::1 - - [03/Aug/2025:04:28:04 +0000] "GET /sw.js HTTP/1.1" 500 945 "http://localhost:3000/sw.js" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::1 - - [03/Aug/2025:04:28:06 +0000] "GET /sw.js HTTP/1.1" 500 945 "http://localhost:3000/sw.js" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::1 - - [03/Aug/2025:04:28:06 +0000] "GET /sw.js HTTP/1.1" 500 945 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::1 - - [03/Aug/2025:04:28:08 +0000] "GET /health HTTP/1.1" 200 110 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::1 - - [03/Aug/2025:04:28:08 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::1 - - [03/Aug/2025:04:28:10 +0000] "GET /sw.js HTTP/1.1" 500 945 "http://localhost:3000/sw.js" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:04:41:03 +0000] "GET /api/v1/feeds HTTP/1.1" 200 137 "-" "WhatsApp/2.2530.2 W"
::ffff:127.0.0.1 - - [03/Aug/2025:04:41:03 +0000] "GET /api/v1/feeds HTTP/1.1" 200 137 "-" "WhatsApp/2.2530.2 W"
::ffff:127.0.0.1 - - [03/Aug/2025:04:41:32 +0000] "GET /api/v1/feeds HTTP/1.1" 200 137 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:41:52 +0000] "POST /api/v1/auth/login HTTP/1.1" 401 55 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:42:42 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 702 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:42:47 +0000] "POST /api/v1/auth/login HTTP/1.1" 401 55 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:43:13 +0000] "POST /api/v1/auth/login HTTP/1.1" 401 55 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:43:35 +0000] "POST /api/v1/auth/login HTTP/1.1" 401 55 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:44:11 +0000] "POST /api/v1/auth/login HTTP/1.1" 401 55 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:44:17 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 137 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:44:36 +0000] "POST /api/v1/auth/login HTTP/1.1" 401 55 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:45:06 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 75 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:45:14 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 703 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:45:17 +0000] "POST /api/v1/auth/login HTTP/1.1" 200 691 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:47:33 +0000] "GET /api/v1/feeds HTTP/1.1" 200 137 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:49:37 +0000] "POST /api/v1/feeds HTTP/1.1" 401 44 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:49:55 +0000] "POST /api/v1/feeds HTTP/1.1" 400 184 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:50:15 +0000] "POST /api/v1/feeds HTTP/1.1" 201 587 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:50:18 +0000] "POST /api/v1/feeds HTTP/1.1" 201 587 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:50:19 +0000] "POST /api/v1/feeds HTTP/1.1" 201 587 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:50:20 +0000] "POST /api/v1/feeds HTTP/1.1" 201 587 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:50:32 +0000] "GET /api/v1/feeds HTTP/1.1" 200 2300 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:51:23 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 2300 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:51:40 +0000] "GET /api/v1/feeds HTTP/1.1" 200 2300 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:53:47 +0000] "GET /api/v1/feeds HTTP/1.1" 200 2268 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:54:52 +0000] "GET /api/v1/feeds HTTP/1.1" 200 2268 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:54:58 +0000] "GET /api/v1/feeds HTTP/1.1" 200 2268 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:54:59 +0000] "GET /api/v1/feeds HTTP/1.1" 200 2268 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:55:17 +0000] "GET /api/v1/feeds HTTP/1.1" 200 2268 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:55:32 +0000] "GET /api/v1/feeds HTTP/1.1" 200 2268 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:04:55:57 +0000] "GET /api/v1/feeds HTTP/1.1" 200 2268 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:02:02 +0000] "POST /api/v1/feeds HTTP/1.1" 201 562 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:02:03 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 500 188 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:02:44 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:03:03 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:03:04 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:03:05 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:03:06 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:03:26 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:03:27 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:03:28 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:03:29 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:05:42 +0000] "POST /api/v1/auth/login HTTP/1.1" 200 682 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:05:51 +0000] "POST /api/v1/auth/login HTTP/1.1" 200 232 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:06:09 +0000] "POST /api/v1/auth/login HTTP/1.1" 200 682 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:07:04 +0000] "POST /api/v1/auth/login HTTP/1.1" 200 568 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:07:34 +0000] "POST /api/v1/auth/login HTTP/1.1" 200 521 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:08:17 +0000] "GET /api/v1/feeds HTTP/1.1" 500 188 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:08:39 +0000] "GET /api/v1/feeds HTTP/1.1" 500 118 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:09:19 +0000] "GET /api/v1/feeds HTTP/1.1" 200 2168 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:09:58 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:10:12 +0000] "GET /api/v1/feeds HTTP/1.1" 200 669 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:10:44 +0000] "POST /api/v1/auth/login HTTP/1.1" 200 692 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:14:24 +0000] "GET /api/v1/feeds HTTP/1.1" 200 669 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:15:37 +0000] "GET /api/v1/feeds HTTP/1.1" 200 591 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:16:15 +0000] "GET /api/v1/feeds HTTP/1.1" 200 553 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:17:13 +0000] "GET /api/v1/feeds HTTP/1.1" 200 553 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:17:43 +0000] "GET /api/v1/feeds HTTP/1.1" 200 639 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:20:01 +0000] "GET /api/v1/feeds HTTP/1.1" 200 553 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:28:02 +0000] "GET /api/v1/feeds HTTP/1.1" 200 553 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:28:54 +0000] "GET /api/v1/feeds HTTP/1.1" 500 116 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:28:55 +0000] "GET /api/v1/feeds HTTP/1.1" 500 116 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:29:22 +0000] "GET /api/v1/feeds HTTP/1.1" 500 116 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:29:36 +0000] "GET /api/v1/feeds HTTP/1.1" 200 553 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:32:36 +0000] "POST /api/v1/reports HTTP/1.1" 201 701 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:35:32 +0000] "POST /api/v1/reports HTTP/1.1" 201 701 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:49:29 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 553 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:49:40 +0000] "GET /api/v1/feeds HTTP/1.1" 200 553 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:56:35 +0000] "POST /api/v1/feeds HTTP/1.1" 401 54 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:58:10 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 50 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:59:33 +0000] "POST /api/v1/auth/register HTTP/1.1" 400 75 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:59:45 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 752 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:05:59:56 +0000] "POST /api/v1/feeds HTTP/1.1" 400 65 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:01:38 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 553 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:02:53 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:03:44 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:03:44 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:03:45 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:03:46 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:04:03 +0000] "POST /api/v1/feeds HTTP/1.1" 400 65 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:04:14 +0000] "POST /api/v1/feeds HTTP/1.1" 400 65 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:04:24 +0000] "POST /api/v1/auth/login HTTP/1.1" 401 55 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:04:47 +0000] "POST /api/v1/auth/login HTTP/1.1" 200 541 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:05:00 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:05:59 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:06:21 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:06:42 +0000] "POST /api/v1/feeds HTTP/1.1" 400 65 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:06:58 +0000] "POST /api/v1/feeds HTTP/1.1" 201 59 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:21:58 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 4035 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:23:21 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 4023 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:23:30 +0000] "GET /api/v1/feeds HTTP/1.1" 200 4023 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:24:17 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1710 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:24:56 +0000] "GET /api/v1/feeds HTTP/1.1" 200 1354 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:25:00 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1354 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:25:23 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1354 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:27:00 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1354 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:28:42 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1354 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:29:06 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1354 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:30:00 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1354 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:32:38 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1354 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:34:59 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1354 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:36:32 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1354 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:37:25 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1354 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:38:20 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1354 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:40:50 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1354 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:42:30 +0000] "GET /api/v1/feeds?page=1&limit=20 HTTP/1.1" 200 1354 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:46:18 +0000] "POST /api/v1/auth/login HTTP/1.1" 200 552 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:06:47:10 +0000] "POST /api/v1/reports HTTP/1.1" 201 701 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:02:46 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 840 "-" "okhttp/5.1.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:03:10 +0000] "POST /api/v1/reports HTTP/1.1" 500 237 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:03:58 +0000] "POST /api/v1/reports HTTP/1.1" 401 54 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:04:05 +0000] "POST /api/v1/reports HTTP/1.1" 400 274 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:04:10 +0000] "POST /api/v1/reports HTTP/1.1" 400 274 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:04:20 +0000] "POST /api/v1/reports HTTP/1.1" 401 43 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:04:40 +0000] "POST /api/v1/reports HTTP/1.1" 400 274 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:04:48 +0000] "POST /api/v1/reports HTTP/1.1" 401 43 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:05:13 +0000] "POST /api/v1/reports HTTP/1.1" 401 44 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:05:39 +0000] "POST /api/v1/reports HTTP/1.1" 401 44 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:05:51 +0000] "POST /api/v1/auth/register HTTP/1.1" 201 752 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:06:03 +0000] "POST /api/v1/reports HTTP/1.1" 500 237 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:06:36 +0000] "POST /api/v1/reports HTTP/1.1" 500 237 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:07:16 +0000] "POST /api/v1/reports HTTP/1.1" 500 237 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:07:17 +0000] "POST /api/v1/reports HTTP/1.1" 201 687 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:07:23 +0000] "POST /api/v1/reports HTTP/1.1" 500 237 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:07:33 +0000] "POST /api/v1/reports HTTP/1.1" 201 687 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:07:55 +0000] "POST /api/v1/reports HTTP/1.1" 500 237 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:08:56 +0000] "POST /api/v1/reports HTTP/1.1" 401 54 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:09:05 +0000] "POST /api/v1/reports HTTP/1.1" 500 237 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:09:31 +0000] "POST /api/v1/reports HTTP/1.1" 201 671 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:09:59 +0000] "POST /api/v1/reports HTTP/1.1" 201 671 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:11:40 +0000] "POST /api/v1/reports HTTP/1.1" 201 687 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:11:54 +0000] "POST /api/v1/reports HTTP/1.1" 201 718 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:12:03 +0000] "POST /api/v1/reports HTTP/1.1" 201 671 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:12:23 +0000] "POST /api/v1/reports HTTP/1.1" 201 702 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:13:23 +0000] "POST /api/v1/reports HTTP/1.1" 201 702 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:14:33 +0000] "POST /api/v1/reports HTTP/1.1" 201 56 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:16:21 +0000] "GET /api/v1/reports HTTP/1.1" 200 1347 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:17:01 +0000] "GET /api/v1/reports HTTP/1.1" 200 1005 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:17:57 +0000] "POST /api/v1/reports HTTP/1.1" 201 56 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:18:23 +0000] "POST /api/v1/reports HTTP/1.1" 201 56 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:18:43 +0000] "POST /api/v1/reports HTTP/1.1" 201 56 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:18:58 +0000] "GET /api/v1/reports HTTP/1.1" 200 639 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:19:03 +0000] "GET /images/image-1754205523009-405426201.png HTTP/1.1" 200 2311076 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:19:03 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:19:36 +0000] "GET /api/v1/reports HTTP/1.1" 200 592 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:20:04 +0000] "POST /api/v1/reports HTTP/1.1" 201 56 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:20:57 +0000] "GET /api/v1/reports HTTP/1.1" 200 1041 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:21:38 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "https://rm3vmd78-3000.inc1.devtunnels.ms/image-1754205604384-876553280.png" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:21:48 +0000] "GET /image-1754205604384-876553280.png HTTP/1.1" 500 1001 "https://rm3vmd78-3000.inc1.devtunnels.ms/image-1754205604384-876553280.png" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:21:49 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "https://rm3vmd78-3000.inc1.devtunnels.ms/image-1754205604384-876553280.png" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:21:58 +0000] "GET //images/image-1754205604384-876553280.png HTTP/1.1" 500 1017 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:21:58 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "https://rm3vmd78-3000.inc1.devtunnels.ms//images/image-1754205604384-876553280.png" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:22:06 +0000] "GET /images/image-1754205604384-876553280.png HTTP/1.1" 200 9075 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:22:06 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "https://rm3vmd78-3000.inc1.devtunnels.ms/images/image-1754205604384-876553280.png" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:23:51 +0000] "GET /api/v1/reports HTTP/1.1" 200 1041 "-" "PostmanRuntime/7.45.0"
::ffff:127.0.0.1 - - [03/Aug/2025:07:39:18 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "https://rm3vmd78-3000.inc1.devtunnels.ms/health" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:39:19 +0000] "GET /health HTTP/1.1" 200 110 "https://rm3vmd78-3000.inc1.devtunnels.ms/health" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:39:20 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "https://rm3vmd78-3000.inc1.devtunnels.ms/health" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:39:26 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "https://rm3vmd78-3000.inc1.devtunnels.ms/health" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:39:29 +0000] "GET /health HTTP/1.1" 200 110 "https://rm3vmd78-3000.inc1.devtunnels.ms/health" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:39:30 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "https://rm3vmd78-3000.inc1.devtunnels.ms/health" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:39:57 +0000] "GET / HTTP/1.1" 500 935 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:39:58 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "https://rm3vmd78-3000.inc1.devtunnels.ms/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:40:25 +0000] "GET /health HTTP/1.1" 200 110 "https://rm3vmd78-3000.inc1.devtunnels.ms/health" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:40:26 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "https://rm3vmd78-3000.inc1.devtunnels.ms/health" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:40:43 +0000] "GET /health HTTP/1.1" 200 110 "https://rm3vmd78-3000.inc1.devtunnels.ms/health" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:40:43 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "https://rm3vmd78-3000.inc1.devtunnels.ms/health" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:43:13 +0000] "GET /health HTTP/1.1" 200 110 "https://rm3vmd78-3000.inc1.devtunnels.ms/health" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
::ffff:127.0.0.1 - - [03/Aug/2025:07:43:13 +0000] "GET /favicon.ico HTTP/1.1" 500 957 "https://rm3vmd78-3000.inc1.devtunnels.ms/health" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
