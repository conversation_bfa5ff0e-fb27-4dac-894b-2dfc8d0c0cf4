const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const commentSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  comment: {
    type: String,
    required: true,
    trim: true,
    maxlength: 500,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

const feedSchema = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    description: {
      type: String,
      required: true,
      trim: true,
      maxlength: 2000,
    },
    image: {
      type: String,
      default: null,
    },
    category: {
      type: String,
      required: true,
      enum: [
        "announcement",
        "event",
        "discussion",
        "news",
        "alert",
        "community",
        "other",
      ],
    },
    // postedAt: {
    //   type: Date,
    //   default: Date.now,
    // },
    likes: [
      {
        type: Schema.Types.ObjectId,
        ref: "User",
      },
    ],
    comments: [commentSchema],
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Virtual for like count
feedSchema.virtual("likeCount").get(function () {
  return this.likes.length;
});

// Virtual for comment count
feedSchema.virtual("commentCount").get(function () {
  return this.comments.length;
});

// Ensure virtual fields are serialized
feedSchema.set("toJSON", { virtuals: true });
feedSchema.set("toObject", { virtuals: true });

module.exports = mongoose.model("Feed", feedSchema);
